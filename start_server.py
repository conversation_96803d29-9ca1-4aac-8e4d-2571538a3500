#!/usr/bin/env python3
"""
服务器启动脚本
包含详细的诊断信息
"""

import sys
import asyncio
import logging
import platform

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_environment():
    """检查运行环境"""
    logger.info("🔍 检查运行环境...")
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"操作系统: {platform.system()} {platform.release()}")
    logger.info(f"架构: {platform.machine()}")
    
    # 检查必要的模块
    required_modules = ['asyncio', 'websockets', 'json', 'uuid']
    for module in required_modules:
        try:
            __import__(module)
            logger.info(f"✅ {module} 模块可用")
        except ImportError:
            logger.error(f"❌ {module} 模块不可用")
            return False
    
    return True

async def simple_websocket_server():
    """最简单的WebSocket服务器"""
    import websockets
    import json
    
    clients = set()
    
    async def handler(websocket, path):
        logger.info(f"📱 新连接: {websocket.remote_address}")
        clients.add(websocket)
        
        try:
            await websocket.send(json.dumps({
                'type': 'welcome',
                'message': '欢迎连接到WebSocket服务器'
            }))
            
            async for message in websocket:
                logger.info(f"📡 收到消息: {message}")
                
                # 回显消息
                await websocket.send(json.dumps({
                    'type': 'echo',
                    'original_message': message,
                    'timestamp': str(asyncio.get_event_loop().time())
                }))
                
        except websockets.exceptions.ConnectionClosed:
            logger.info("🔌 连接已关闭")
        except Exception as e:
            logger.error(f"❌ 连接错误: {e}")
        finally:
            clients.discard(websocket)
            logger.info(f"🔌 连接已移除，剩余连接数: {len(clients)}")
    
    # 启动服务器
    server = await websockets.serve(handler, "127.0.0.1", 8765)
    logger.info("✅ WebSocket服务器已启动: ws://127.0.0.1:8765")
    
    # 保持运行
    try:
        await server.wait_closed()
    except Exception as e:
        logger.error(f"❌ 服务器错误: {e}")
    finally:
        server.close()
        await server.wait_closed()
        logger.info("🛑 服务器已关闭")

async def test_event_loop():
    """测试事件循环"""
    logger.info("🧪 测试事件循环...")
    
    for i in range(5):
        logger.info(f"⏰ 计数: {i}")
        await asyncio.sleep(1)
    
    logger.info("✅ 事件循环测试完成")

def main():
    """主函数"""
    logger.info("🚀 启动服务器诊断程序...")
    
    # 检查环境
    if not check_environment():
        logger.error("❌ 环境检查失败")
        return
    
    # 测试事件循环
    logger.info("🧪 测试基本事件循环...")
    try:
        asyncio.run(test_event_loop())
        logger.info("✅ 事件循环正常")
    except Exception as e:
        logger.error(f"❌ 事件循环测试失败: {e}")
        return
    
    # 启动WebSocket服务器
    logger.info("🚀 启动WebSocket服务器...")
    try:
        asyncio.run(simple_websocket_server())
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号")
    except Exception as e:
        logger.error(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        logger.info("🛑 程序结束")

if __name__ == "__main__":
    main()
