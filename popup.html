<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>WeChat Image Monitor</title>
  <style>
    body {
      width: 300px;
      padding: 15px;
      font-family: Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      font-weight: bold;
    }
    
    .status {
      background: rgba(255, 255, 255, 0.1);
      padding: 10px;
      border-radius: 8px;
      margin-bottom: 15px;
    }
    
    .status-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
    
    .detection-list {
      max-height: 200px;
      overflow-y: auto;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 10px;
    }
    
    .detection-item {
      background: rgba(255, 255, 255, 0.2);
      padding: 8px;
      margin-bottom: 8px;
      border-radius: 5px;
      font-size: 12px;
    }
    
    .detection-time {
      color: #ffd700;
      font-weight: bold;
    }
    
    .detection-type {
      color: #00ff88;
      font-weight: bold;
      margin-top: 3px;
    }
    
    .detection-url {
      word-break: break-all;
      margin-top: 5px;
    }
    
    .clear-btn {
      background: #ff4757;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 5px;
      cursor: pointer;
      width: 100%;
      margin-top: 10px;
    }
    
    .clear-btn:hover {
      background: #ff3742;
    }
    
    .test-btn {
      background: #17a2b8;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 5px;
      cursor: pointer;
      width: 100%;
      margin-top: 10px;
    }
    
    .test-btn:hover {
      background: #138496;
    }
    
    .test-info {
      position: fixed;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 10px 15px;
      border-radius: 5px;
      text-align: center;
      z-index: 1000;
      animation: fadeIn 0.3s ease;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translate(-50%, 20px); }
      to { opacity: 1; transform: translate(-50%, 0); }
    }
    
    .getnewmsgnum-list {
      max-height: 200px;
      overflow-y: auto;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 10px;
      margin-bottom: 15px;
    }
    
    .getnewmsgnum-item {
      background: rgba(0, 150, 136, 0.2);
      padding: 8px;
      margin-bottom: 8px;
      border-radius: 5px;
      font-size: 12px;
    }
    
    .getnewmsgnum-time {
      color: #ffd700;
      font-weight: bold;
    }
    
    .getnewmsgnum-method {
      color: #00bcd4;
      font-weight: bold;
      margin-top: 3px;
    }
    
    .getnewmsgnum-count {
      color: #ff5722;
      font-weight: bold;
      margin-top: 3px;
    }
    
    .getnewmsgnum-url {
      word-break: break-all;
      margin-top: 5px;
    }
    
    .no-detections {
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
      font-style: italic;
    }
    
    .download-list {
      max-height: 150px;
      overflow-y: auto;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 10px;
      margin-top: 15px;
    }
    
    .download-item {
      background: rgba(0, 255, 136, 0.2);
      padding: 8px;
      margin-bottom: 8px;
      border-radius: 5px;
      font-size: 12px;
    }
    
    .download-time {
      color: #00ff88;
      font-weight: bold;
    }
    
    .download-filename {
      color: #ffd700;
      font-weight: bold;
      margin-top: 3px;
    }
    
    .download-msgid {
      color: #ff6b6b;
      font-weight: bold;
      margin-top: 3px;
    }
    
    .download-tofakeid {
      color: #4ecdc4;
      font-weight: bold;
      margin-top: 3px;
    }
    
    .no-downloads {
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
      font-style: italic;
    }
    
    .cache-skip-list {
      max-height: 120px;
      overflow-y: auto;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 10px;
      margin-top: 15px;
    }
    
    .cache-skip-item {
      background: rgba(255, 193, 7, 0.2);
      padding: 8px;
      margin-bottom: 8px;
      border-radius: 5px;
      font-size: 12px;
    }
    
    .cache-skip-time {
      color: #ffc107;
      font-weight: bold;
    }
    
    .cache-skip-msgid {
      color: #ff6b6b;
      font-weight: bold;
      margin-top: 3px;
    }
    
    .cache-skip-tofakeid {
      color: #4ecdc4;
      font-weight: bold;
      margin-top: 3px;
    }
    
    .cache-skip-remaining {
      color: #ffd700;
      font-weight: bold;
      margin-top: 3px;
    }
    
    .no-cache-skips {
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
      font-style: italic;
    }
    
    .new-message-click-list {
      max-height: 100px;
      overflow-y: auto;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      padding: 10px;
      margin-top: 15px;
    }
    
    .new-message-click-item {
      background: rgba(220, 53, 69, 0.2);
      padding: 8px;
      margin-bottom: 8px;
      border-radius: 5px;
      font-size: 12px;
    }
    
    .new-message-click-time {
      color: #dc3545;
      font-weight: bold;
    }
    
    .new-message-click-status {
      color: #28a745;
      font-weight: bold;
      margin-top: 3px;
    }
    
    .no-new-message-clicks {
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🔍 WeChat Image Monitor</h1>
  </div>
  
  <div class="status">
    <div class="status-item">
      <span>状态:</span>
      <span id="status">监听中...</span>
    </div>
    <div class="status-item">
      <span>目标URL:</span>
      <span id="targetUrl">mp.weixin.qq.com/cgi-bin/getimgdata</span>
    </div>
    <div class="status-item">
      <span>检测次数:</span>
      <span id="detectionCount">0</span>
    </div>
  </div>
  
  <div class="detection-list">
    <h3>检测记录</h3>
    <div id="detections">
      <div class="no-detections">暂无检测记录</div>
    </div>
  </div>
  
  <div class="download-list">
    <h3>下载记录</h3>
    <div id="downloads">
      <div class="no-downloads">暂无下载记录</div>
    </div>
  </div>
  
  <div class="cache-skip-list">
    <h3>缓存跳过记录</h3>
    <div id="cacheSkips">
      <div class="no-cache-skips">暂无缓存跳过记录</div>
    </div>
  </div>
  
  <div class="new-message-click-list">
    <h3>新消息点击记录</h3>
    <div id="newMessageClicks">
      <div class="no-new-message-clicks">暂无新消息点击记录</div>
    </div>
  </div>
  
  <button class="clear-btn" id="clearBtn">清空记录</button>
  <button class="test-btn" id="testBtn">测试新消息监听</button>
  
  <script src="popup.js"></script>
</body>
</html> 