<?php
/**
 * PHP调用Chrome扩展的示例代码
 * 展示如何在您的PHP项目中触发Chrome扩展执行点击操作
 */

/**
 * Chrome扩展触发器类
 */
class ChromeExtensionTrigger {
    private $apiUrl;
    
    public function __construct($apiUrl = 'http://127.0.0.1:9999') {
        $this->apiUrl = rtrim($apiUrl, '/');
    }
    
    /**
     * 触发Chrome扩展执行点击序列
     * 
     * @param array $data 可选的附加数据
     * @return array 响应结果
     */
    public function triggerClick($data = []) {
        $url = $this->apiUrl . '/api/trigger-chrome-click';
        
        $postData = array_merge([
            'timestamp' => date('Y-m-d H:i:s'),
            'source' => 'php_project'
        ], $data);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'User-Agent: PHP-Chrome-Extension-Client'
                ],
                'content' => json_encode($postData),
                'timeout' => 10
            ]
        ]);
        
        try {
            $response = file_get_contents($url, false, $context);
            if ($response === false) {
                throw new Exception('请求失败');
            }
            
            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('响应解析失败: ' . json_last_error_msg());
            }
            
            return $result;
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 检查状态
     */
    public function getStatus() {
        $url = $this->apiUrl . '/api/status';
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'timeout' => 5
            ]
        ]);
        
        try {
            $response = file_get_contents($url, false, $context);
            if ($response === false) {
                throw new Exception('请求失败');
            }
            
            return json_decode($response, true);
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 等待执行完成
     * 
     * @param int $timeout 超时时间（秒）
     * @return array 结果
     */
    public function waitForCompletion($timeout = 30) {
        $startTime = time();
        
        while (time() - $startTime < $timeout) {
            $status = $this->getStatus();
            
            if (!$status['success']) {
                return [
                    'success' => false,
                    'error' => '无法获取状态: ' . ($status['error'] ?? 'unknown')
                ];
            }
            
            // 如果execute为false且有确认时间，说明执行完成
            if (!$status['execute'] && !empty($status['last_confirm_time'])) {
                return [
                    'success' => true,
                    'message' => '执行完成',
                    'status' => $status
                ];
            }
            
            sleep(2); // 等待2秒后再检查
        }
        
        return [
            'success' => false,
            'error' => '等待超时'
        ];
    }
    
    /**
     * 触发并等待完成
     */
    public function triggerAndWait($data = [], $timeout = 30) {
        // 先触发
        $triggerResult = $this->triggerClick($data);
        if (!$triggerResult['success']) {
            return $triggerResult;
        }
        
        // 等待完成
        return $this->waitForCompletion($timeout);
    }
}

// ==================== 使用示例 ====================

echo "🚀 Chrome扩展PHP调用示例\n";
echo "========================\n\n";

// 创建触发器实例
$trigger = new ChromeExtensionTrigger();

// 示例1: 简单触发
echo "📝 示例1: 简单触发\n";
echo "-------------------\n";
$result = $trigger->triggerClick();
if ($result['success']) {
    echo "✅ 触发成功！触发ID: " . $result['trigger_id'] . "\n";
} else {
    echo "❌ 触发失败: " . $result['error'] . "\n";
}
echo "\n";

// 示例2: 带数据触发
echo "📝 示例2: 带数据触发\n";
echo "-------------------\n";
$result = $trigger->triggerClick([
    'user_id' => 12345,
    'action' => 'process_user_data',
    'priority' => 'high'
]);
if ($result['success']) {
    echo "✅ 带数据触发成功！触发ID: " . $result['trigger_id'] . "\n";
} else {
    echo "❌ 触发失败: " . $result['error'] . "\n";
}
echo "\n";

// 示例3: 触发并等待完成
echo "📝 示例3: 触发并等待完成\n";
echo "------------------------\n";
echo "🔄 触发并等待执行完成...\n";
$result = $trigger->triggerAndWait([
    'operation' => 'batch_process',
    'items' => ['item1', 'item2', 'item3']
], 30);

if ($result['success']) {
    echo "🎉 执行完成！\n";
    echo "📊 最终状态:\n";
    echo "   - 触发次数: " . $result['status']['trigger_count'] . "\n";
    echo "   - 最后确认: " . $result['status']['last_confirm_time'] . "\n";
} else {
    echo "❌ 执行失败: " . $result['error'] . "\n";
}
echo "\n";

// 示例4: 检查状态
echo "📝 示例4: 检查状态\n";
echo "------------------\n";
$status = $trigger->getStatus();
if ($status['success']) {
    echo "📊 当前状态:\n";
    echo "   - 执行状态: " . ($status['execute'] ? '等待执行' : '空闲') . "\n";
    echo "   - 触发次数: " . ($status['trigger_count'] ?? 0) . "\n";
    echo "   - 最后触发: " . ($status['last_trigger_time'] ?? '无') . "\n";
    echo "   - 最后确认: " . ($status['last_confirm_time'] ?? '无') . "\n";
} else {
    echo "❌ 获取状态失败: " . $status['error'] . "\n";
}
echo "\n";

// ==================== 实际应用示例 ====================

echo "💼 实际应用示例\n";
echo "===============\n\n";

/**
 * 示例: 在用户注册后触发Chrome扩展
 */
function onUserRegistered($userId, $userData) {
    $trigger = new ChromeExtensionTrigger();
    
    echo "👤 用户注册: $userId\n";
    
    // 触发Chrome扩展执行相关操作
    $result = $trigger->triggerClick([
        'event' => 'user_registered',
        'user_id' => $userId,
        'user_data' => $userData,
        'timestamp' => time()
    ]);
    
    if ($result['success']) {
        echo "✅ Chrome扩展已触发处理用户注册\n";
        // 可以记录到数据库或日志
        error_log("Chrome extension triggered for user registration: $userId");
    } else {
        echo "❌ Chrome扩展触发失败: " . $result['error'] . "\n";
        // 错误处理
        error_log("Failed to trigger Chrome extension for user: $userId - " . $result['error']);
    }
}

/**
 * 示例: 在订单处理时触发Chrome扩展
 */
function processOrder($orderId) {
    $trigger = new ChromeExtensionTrigger();
    
    echo "📦 处理订单: $orderId\n";
    
    // 触发并等待完成
    $result = $trigger->triggerAndWait([
        'event' => 'order_processing',
        'order_id' => $orderId,
        'priority' => 'normal'
    ], 60); // 等待最多60秒
    
    if ($result['success']) {
        echo "✅ 订单处理完成，Chrome扩展操作已执行\n";
        return true;
    } else {
        echo "❌ Chrome扩展操作失败: " . $result['error'] . "\n";
        return false;
    }
}

/**
 * 示例: 定时任务中使用
 */
function cronJobExample() {
    $trigger = new ChromeExtensionTrigger();
    
    echo "⏰ 执行定时任务\n";
    
    // 检查是否有待处理的任务
    $status = $trigger->getStatus();
    if ($status['execute']) {
        echo "⚠️ Chrome扩展正在执行中，跳过此次任务\n";
        return;
    }
    
    // 触发定时任务
    $result = $trigger->triggerClick([
        'event' => 'cron_job',
        'job_type' => 'daily_maintenance',
        'scheduled_time' => date('Y-m-d H:i:s')
    ]);
    
    if ($result['success']) {
        echo "✅ 定时任务已触发Chrome扩展\n";
    } else {
        echo "❌ 定时任务触发失败: " . $result['error'] . "\n";
    }
}

// 运行示例
echo "🧪 运行实际应用示例:\n\n";

// 模拟用户注册
onUserRegistered(12345, ['name' => 'John Doe', 'email' => '<EMAIL>']);
echo "\n";

// 模拟订单处理
processOrder('ORDER-2024-001');
echo "\n";

// 模拟定时任务
cronJobExample();
echo "\n";

echo "🎉 所有示例执行完成！\n";
echo "\n";
echo "💡 提示:\n";
echo "1. 确保Chrome扩展已安装并启用\n";
echo "2. 确保接口服务器已启动: php -S 127.0.0.1:9999 php-project-example.php\n";
echo "3. 在实际项目中，请根据需要调整错误处理和日志记录\n";
?>
