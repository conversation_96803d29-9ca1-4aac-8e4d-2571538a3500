# WeChat Image Monitor Chrome 插件

这是一个Chrome浏览器插件，用于监听微信公众号图片数据包。当检测到目标URL `https://mp.weixin.qq.com/cgi-bin/getimgdata` 的请求时，会在控制台打印相关信息。

## 功能特性

- 🔍 **实时监听**: 监听所有网络请求，检测目标URL
- 📊 **详细记录**: 记录检测时间、URL、请求详情等信息
- 🎨 **美观界面**: 现代化的popup界面，显示检测记录
- 💾 **数据持久化**: 检测记录保存在本地存储中
- 🔄 **多种检测方式**: 支持webRequest、页面图片、fetch、XHR等多种检测方式
- 🎯 **页面元素监听**: 监听特定元素出现并自动执行点击操作
- ⏰ **延时点击序列**: 支持按时间间隔执行多个点击操作

## 安装方法

1. 下载所有文件到本地文件夹
2. 打开Chrome浏览器，进入扩展程序页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含插件文件的文件夹

## 使用方法

1. 安装插件后，点击浏览器工具栏中的插件图标
2. 插件会自动开始监听目标URL
3. 当检测到目标图片请求时，会在以下位置显示信息：
   - 浏览器控制台（F12打开开发者工具）
   - 插件popup界面
   - 插件图标会显示检测次数

## 文件结构

```
├── manifest.json      # 插件配置文件
├── background.js      # 后台脚本，监听网络请求
├── popup.html        # 弹出窗口界面
├── popup.js          # 弹出窗口脚本
├── content.js        # 内容脚本，页面注入
└── README.md         # 说明文档
```

## 权限说明

插件需要以下权限：
- `webRequest`: 监听网络请求
- `webRequestFilter`: 过滤特定URL的请求
- `activeTab`: 访问当前标签页
- `storage`: 保存检测记录
- `host_permissions`: 访问微信公众号域名

## 检测方式

插件通过多种方式检测目标URL：

1. **webRequest API**: 监听所有网络请求
2. **页面图片检测**: 检测页面中的img标签
3. **fetch拦截**: 拦截JavaScript的fetch请求
4. **XHR拦截**: 拦截XMLHttpRequest请求

## 注意事项

- 插件仅在检测到目标URL时才会记录和显示信息
- 检测记录最多保存50条，超出会自动清理
- 所有检测信息都会在浏览器控制台打印
- 插件需要访问微信公众号相关域名才能正常工作

## 技术实现

- 使用Chrome Extension Manifest V3
- 采用Service Worker作为后台脚本
- 使用chrome.storage.local进行数据持久化
- 支持实时消息传递机制

## 页面元素监听功能

### 功能说明
当页面中出现 `#app > div.msg-tips` 元素时，插件会自动执行以下操作：

1. **第一次点击**：立即点击 `#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(1)`

2. **第二次点击**：等待10秒后点击 `#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(2)`

### 监控记录
- 插件会记录所有的元素点击操作
- 在popup界面中可以查看元素点击记录
- 包含点击时间、元素类型和选择器信息

### 测试方法
1. 使用提供的 `test.html` 文件进行功能测试
2. 打开测试页面，点击"显示 msg-tips 元素"按钮
3. 观察扩展是否自动点击了两个目标元素
4. 查看浏览器控制台和扩展popup中的记录

## 更新日志

### v1.1
- 🎯 新增页面元素监听功能
- ⏰ 支持延时点击序列（10秒间隔）
- 📝 新增元素点击记录显示
- 🧪 提供测试页面验证功能

### v1.0
- 初始版本发布
- 支持基本的图片请求监听
- 提供popup界面显示检测记录
- 支持多种检测方式