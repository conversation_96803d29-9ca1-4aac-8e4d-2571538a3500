# WeChat Image Monitor Chrome 插件

这是一个Chrome浏览器插件，用于监听微信公众号图片数据包和自动执行页面元素点击操作。

## 功能特性

- 🔍 **实时监听**: 监听所有网络请求，检测目标URL
- 📊 **详细记录**: 记录检测时间、URL、请求详情等信息
- 🎨 **美观界面**: 现代化的popup界面，显示检测记录
- 💾 **数据持久化**: 检测记录保存在本地存储中
- 🔄 **多种检测方式**: 支持webRequest、页面图片、fetch、XHR等多种检测方式
- 🎯 **页面元素监听**: 监听特定元素出现并自动执行点击操作
- ⏰ **延时点击序列**: 支持按时间间隔执行多个点击操作
- 🌐 **API集成**: 图片下载后自动发送HTTP请求到本地API
- 🚀 **PHP触发服务**: 通过PHP脚本远程触发点击序列执行
- 🔗 **WebSocket集成**: 基于WebSocket的实时通信，低延迟高效率

## 安装方法

1. 下载所有文件到本地文件夹
2. 打开Chrome浏览器，进入扩展程序页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含插件文件的文件夹

## 使用方法

1. 安装插件后，点击浏览器工具栏中的插件图标
2. 插件会自动开始监听目标URL
3. 当检测到目标图片请求时，会在以下位置显示信息：
   - 浏览器控制台（F12打开开发者工具）
   - 插件popup界面
   - 插件图标会显示检测次数

## 文件结构

```
├── manifest.json      # 插件配置文件
├── background.js      # 后台脚本，监听网络请求
├── popup.html        # 弹出窗口界面
├── popup.js          # 弹出窗口脚本
├── content.js        # 内容脚本，页面注入
└── README.md         # 说明文档
```

## 权限说明

插件需要以下权限：
- `webRequest`: 监听网络请求
- `webRequestFilter`: 过滤特定URL的请求
- `activeTab`: 访问当前标签页
- `storage`: 保存检测记录
- `host_permissions`: 访问微信公众号域名

## 检测方式

插件通过多种方式检测目标URL：

1. **webRequest API**: 监听所有网络请求
2. **页面图片检测**: 检测页面中的img标签
3. **fetch拦截**: 拦截JavaScript的fetch请求
4. **XHR拦截**: 拦截XMLHttpRequest请求

## 注意事项

- 插件仅在检测到目标URL时才会记录和显示信息
- 检测记录最多保存50条，超出会自动清理
- 所有检测信息都会在浏览器控制台打印
- 插件需要访问微信公众号相关域名才能正常工作

## 技术实现

- 使用Chrome Extension Manifest V3
- 采用Service Worker作为后台脚本
- 使用chrome.storage.local进行数据持久化
- 支持实时消息传递机制

## 页面标题监听功能

### 功能说明
当页面标题包含"私"字时，插件会自动执行以下操作：

1. **第一次点击**：立即点击 `#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(1)`

2. **第二次点击**：等待10秒后点击 `#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(2)`

### 监控记录
- 插件会记录所有的元素点击操作
- 在popup界面中可以查看元素点击记录
- 包含点击时间、元素类型和选择器信息

### 测试方法
1. 使用提供的 `test-title.html` 文件进行功能测试
2. 打开测试页面，点击"设置包含'私'字的标题"按钮
3. 观察扩展是否自动点击了两个目标元素
4. 查看浏览器控制台和扩展popup中的记录
5. 可以使用"开始自动切换标题"功能测试无限循环监听

## API集成功能

### 功能说明
当图片下载成功后，扩展会自动发送HTTP GET请求到本地API：
- **API地址**: `http://127.0.0.1/api/send?openid={tofakeid}`
- **请求方法**: GET
- **参数**: openid = tofakeid（从singlesendpage请求中获取）

### API请求流程
1. 图片下载成功后自动触发
2. 使用tofakeid作为openid参数
3. 发送GET请求到本地API
4. 记录请求结果（成功/失败）
5. 在popup界面显示API请求记录

### API记录显示
- 请求时间和状态（成功/失败）
- tofakeid参数值
- 完整的API请求URL
- 响应内容或错误信息
- 支持查看最近20条API请求记录

### 本地API服务器设置
1. **启动测试服务器**：
   ```bash
   node test-api-server.js
   ```

2. **API服务器要求**：
   - 监听127.0.0.1:80端口
   - 支持GET请求到 `/api/send?openid={tofakeid}`
   - 设置正确的CORS头部

3. **故障排除**：
   - 如果遇到"Failed to fetch"错误，请查看 `API_TROUBLESHOOTING.md`
   - 确保本地API服务器已启动并可访问
   - 检查防火墙和权限设置

## PHP项目集成

### 功能说明
Chrome扩展可以监听来自PHP项目的HTTP请求，当收到触发指令时自动执行点击序列操作。

### 工作原理
```
您的PHP项目 → HTTP API接口 ← Chrome扩展轮询
     ↓              ↓              ↓
  发送触发请求   →  设置执行标志  →  执行点击操作
```

### 快速开始
1. **部署API接口**：
   ```bash
   php -S 127.0.0.1:9999 php-project-example.php
   ```

2. **重新加载Chrome扩展**：确保扩展启用PHP监听功能

3. **在PHP项目中调用**：
   ```php
   $trigger = new ChromeExtensionTrigger();
   $result = $trigger->triggerClick(['user_id' => 123]);
   ```

### API端点
- `POST /api/trigger-chrome-click`: PHP项目调用触发扩展
- `GET /chrome-extension/check`: 扩展轮询检查触发指令
- `POST /chrome-extension/confirm`: 扩展发送执行确认
- `GET /api/status`: 查看当前状态

### 实际应用示例
```php
// 用户注册后触发
function onUserRegistered($userId) {
    $trigger = new ChromeExtensionTrigger();
    $result = $trigger->triggerClick([
        'event' => 'user_registered',
        'user_id' => $userId
    ]);
    return $result['success'];
}
```

### 详细文档
查看 `PHP_INTEGRATION_GUIDE.md` 获取完整的集成指南和示例。

## WebSocket实时集成

### 功能说明
基于WebSocket的实时通信方案，相比HTTP轮询具有更低延迟和更高效率。

### 优势特点
- **低延迟**: 实时双向通信，无需轮询等待
- **高效率**: 减少网络请求，降低服务器负载
- **实时确认**: 立即获得执行结果反馈

### 快速开始
1. **安装依赖**：
   ```bash
   composer install
   ```

2. **启动WebSocket服务器**：
   ```bash
   php websocket-server.php
   ```

3. **在PHP项目中使用**：
   ```php
   require_once 'ChromeExtensionTrigger.php';

   $trigger = new ChromeExtensionTrigger();
   $result = $trigger->triggerAndWait(['user_id' => 123], 30);

   if ($result['success']) {
       echo "执行完成！";
   }
   ```

### 实际应用
```php
// 用户注册后触发
function onUserRegistered($userId) {
    $trigger = new ChromeExtensionTrigger();
    return $trigger->triggerClick([
        'event' => 'user_registered',
        'user_id' => $userId
    ]);
}
```

### 详细文档
查看 `WEBSOCKET_INTEGRATION_GUIDE.md` 获取完整的WebSocket集成指南。

## 更新日志

### v1.6
- 🔗 新增WebSocket实时集成功能
- ⚡ 实现低延迟的实时双向通信
- 🚀 提供WebSocket服务器和PHP客户端
- 📡 支持实时触发确认和状态反馈
- 🛠️ 完整的Composer包管理和依赖

### v1.5
- 🚀 新增PHP触发服务功能
- 📡 支持通过PHP脚本远程触发点击序列
- 🔄 实现HTTP API接口用于PHP通信
- 📝 提供完整的PHP客户端示例和文档
- 🎯 优化msgid匹配逻辑（支持lastmsgid-1匹配）

### v1.4
- 📝 修改监听方式：从监听DOM元素改为监听页面标题
- 🎯 新的触发条件：页面标题包含"私"字时触发点击操作
- 🔄 优化无限循环监听机制
- 🧪 提供新的测试页面 `test-title.html`

### v1.3
- 🌐 新增API集成功能
- 📡 图片下载后自动发送HTTP请求到本地API
- 📝 新增API请求记录显示
- 🔍 支持查看API请求成功/失败状态

### v1.2
- 🗑️ 移除getnewmsgnum监听功能
- 🧹 清理相关代码和界面元素
- 🎯 专注于页面元素监听和图片下载功能

### v1.1
- 🎯 新增页面元素监听功能
- ⏰ 支持延时点击序列（10秒间隔）
- 📝 新增元素点击记录显示
- 🧪 提供测试页面验证功能

### v1.0
- 初始版本发布
- 支持基本的图片请求监听
- 提供popup界面显示检测记录
- 支持多种检测方式