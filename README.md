# WeChat Image Monitor Chrome 插件

这是一个Chrome浏览器插件，用于监听微信公众号图片数据包和自动执行页面元素点击操作。

## 功能特性

- 🔍 **实时监听**: 监听所有网络请求，检测目标URL
- 📊 **详细记录**: 记录检测时间、URL、请求详情等信息
- 🎨 **美观界面**: 现代化的popup界面，显示检测记录
- 💾 **数据持久化**: 检测记录保存在本地存储中
- 🔄 **多种检测方式**: 支持webRequest、页面图片、fetch、XHR等多种检测方式
- 🎯 **页面元素监听**: 监听特定元素出现并自动执行点击操作
- ⏰ **延时点击序列**: 支持按时间间隔执行多个点击操作
- 🌐 **API集成**: 图片下载后自动发送HTTP请求到本地API
- 🐍 **Python WebSocket集成**: 通过Python WebSocket服务器接收PHP请求并与扩展通信

## 安装方法

1. 下载所有文件到本地文件夹
2. 打开Chrome浏览器，进入扩展程序页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含插件文件的文件夹

## 使用方法

1. 安装插件后，点击浏览器工具栏中的插件图标
2. 插件会自动开始监听目标URL
3. 当检测到目标图片请求时，会在以下位置显示信息：
   - 浏览器控制台（F12打开开发者工具）
   - 插件popup界面
   - 插件图标会显示检测次数

## 文件结构

```
├── manifest.json      # 插件配置文件
├── background.js      # 后台脚本，监听网络请求
├── popup.html        # 弹出窗口界面
├── popup.js          # 弹出窗口脚本
├── content.js        # 内容脚本，页面注入
└── README.md         # 说明文档
```

## 权限说明

插件需要以下权限：
- `webRequest`: 监听网络请求
- `webRequestFilter`: 过滤特定URL的请求
- `activeTab`: 访问当前标签页
- `storage`: 保存检测记录
- `host_permissions`: 访问微信公众号域名

## 检测方式

插件通过多种方式检测目标URL：

1. **webRequest API**: 监听所有网络请求
2. **页面图片检测**: 检测页面中的img标签
3. **fetch拦截**: 拦截JavaScript的fetch请求
4. **XHR拦截**: 拦截XMLHttpRequest请求

## 注意事项

- 插件仅在检测到目标URL时才会记录和显示信息
- 检测记录最多保存50条，超出会自动清理
- 所有检测信息都会在浏览器控制台打印
- 插件需要访问微信公众号相关域名才能正常工作

## 技术实现

- 使用Chrome Extension Manifest V3
- 采用Service Worker作为后台脚本
- 使用chrome.storage.local进行数据持久化
- 支持实时消息传递机制

## 页面标题监听功能

### 功能说明
当页面标题包含"私"字时，插件会自动执行以下操作：

1. **第一次点击**：立即点击 `#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(1)`

2. **第二次点击**：等待10秒后点击 `#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(2)`

### 监控记录
- 插件会记录所有的元素点击操作
- 在popup界面中可以查看元素点击记录
- 包含点击时间、元素类型和选择器信息

### 测试方法
1. 使用提供的 `test-title.html` 文件进行功能测试
2. 打开测试页面，点击"设置包含'私'字的标题"按钮
3. 观察扩展是否自动点击了两个目标元素
4. 查看浏览器控制台和扩展popup中的记录
5. 可以使用"开始自动切换标题"功能测试无限循环监听

## API集成功能

### 功能说明
当图片下载成功后，扩展会自动发送HTTP GET请求到本地API：
- **API地址**: `http://127.0.0.1/api/send?openid={tofakeid}`
- **请求方法**: GET
- **参数**: openid = tofakeid（从singlesendpage请求中获取）

### API请求流程
1. 图片下载成功后自动触发
2. 使用tofakeid作为openid参数
3. 发送GET请求到本地API
4. 记录请求结果（成功/失败）
5. 在popup界面显示API请求记录

### API记录显示
- 请求时间和状态（成功/失败）
- tofakeid参数值
- 完整的API请求URL
- 响应内容或错误信息
- 支持查看最近20条API请求记录

### 本地API服务器设置
1. **启动测试服务器**：
   ```bash
   node test-api-server.js
   ```

2. **API服务器要求**：
   - 监听127.0.0.1:80端口
   - 支持GET请求到 `/api/send?openid={tofakeid}`
   - 设置正确的CORS头部

3. **故障排除**：
   - 如果遇到"Failed to fetch"错误，请查看 `API_TROUBLESHOOTING.md`
   - 确保本地API服务器已启动并可访问
   - 检查防火墙和权限设置

## Python WebSocket集成

### 功能说明
通过Python WebSocket服务器接收PHP的HTTP请求，并通过WebSocket与Chrome扩展进行实时通信。

### 架构优势
```
PHP项目 → HTTP请求 → Python WebSocket服务器 → WebSocket → Chrome扩展
   ↑                        ↓                        ↓           ↓
   ←─── HTTP响应 ←─── 状态管理 ←─── WebSocket消息 ←─── 执行点击操作
```

- **高性能**: Python异步处理，支持高并发
- **实时通信**: WebSocket双向通信，低延迟
- **状态管理**: 完整的请求状态跟踪
- **易于集成**: 简单的HTTP API接口

### 快速开始
1. **安装Python依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **启动Python WebSocket服务器**：
   ```bash
   python websocket_server.py
   ```

3. **重新加载Chrome扩展**：确保扩展连接到Python服务器

4. **在PHP项目中调用**：
   ```php
   // 简单触发
   $data = json_encode(['user_id' => 123, 'action' => 'process_user']);
   $response = file_get_contents('http://127.0.0.1:8080/trigger', false,
       stream_context_create([
           'http' => [
               'method' => 'POST',
               'header' => 'Content-Type: application/json',
               'content' => $data
           ]
       ])
   );
   $result = json_decode($response, true);
   ```

### HTTP API接口
- `POST /trigger`: 触发Chrome扩展执行点击操作
- `GET /status`: 查询服务器或请求状态
- `POST /wait`: 等待请求执行完成

### 实际应用示例
```php
// 用户注册后触发
function onUserRegistered($userId, $userData) {
    $result = triggerChromeExtension([
        'event' => 'user_registered',
        'user_id' => $userId,
        'user_data' => $userData
    ]);
    return $result['success'];
}

// 订单处理时触发并等待
function processOrder($orderId) {
    $result = triggerAndWait([
        'event' => 'order_processing',
        'order_id' => $orderId
    ], 60);
    return $result['completed'] && $result['result']['success'];
}
```

### 测试和调试
```bash
# 测试Python客户端
python python_client_example.py

# 检查服务器状态
curl http://127.0.0.1:8080/status
```

### 详细文档
查看 `PYTHON_INTEGRATION_GUIDE.md` 获取完整的集成指南和API文档。

## 更新日志

### v2.0
- 🐍 **重构为Python WebSocket架构**
- ⚡ 高性能异步Python WebSocket服务器
- 🔄 PHP通过HTTP请求触发，Python通过WebSocket与扩展通信
- 📊 完整的请求状态管理和跟踪
- 🚀 支持触发并等待执行完成
- 📡 实时双向通信，低延迟高效率
- 🛠️ 简化部署，只需Python环境
- 📝 完整的API文档和使用示例

### v1.6
- 🔗 新增WebSocket实时集成功能
- ⚡ 实现低延迟的实时双向通信
- 🚀 提供WebSocket服务器和PHP客户端
- 📡 支持实时触发确认和状态反馈
- 🛠️ 完整的Composer包管理和依赖

### v1.5
- 🚀 新增PHP触发服务功能
- 📡 支持通过PHP脚本远程触发点击序列
- 🔄 实现HTTP API接口用于PHP通信
- 📝 提供完整的PHP客户端示例和文档
- 🎯 优化msgid匹配逻辑（支持lastmsgid-1匹配）

### v1.4
- 📝 修改监听方式：从监听DOM元素改为监听页面标题
- 🎯 新的触发条件：页面标题包含"私"字时触发点击操作
- 🔄 优化无限循环监听机制
- 🧪 提供新的测试页面 `test-title.html`

### v1.3
- 🌐 新增API集成功能
- 📡 图片下载后自动发送HTTP请求到本地API
- 📝 新增API请求记录显示
- 🔍 支持查看API请求成功/失败状态

### v1.2
- 🗑️ 移除getnewmsgnum监听功能
- 🧹 清理相关代码和界面元素
- 🎯 专注于页面元素监听和图片下载功能

### v1.1
- 🎯 新增页面元素监听功能
- ⏰ 支持延时点击序列（10秒间隔）
- 📝 新增元素点击记录显示
- 🧪 提供测试页面验证功能

### v1.0
- 初始版本发布
- 支持基本的图片请求监听
- 提供popup界面显示检测记录
- 支持多种检测方式