// 简单的测试API服务器
// 使用Node.js运行: node test-api-server.js

const http = require('http');
const url = require('url');

const server = http.createServer((req, res) => {
  // 设置CORS头部
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');
  
  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const query = parsedUrl.query;
  
  console.log(`收到请求: ${req.method} ${req.url}`);
  console.log('查询参数:', query);
  
  if (pathname === '/api/send' && req.method === 'GET') {
    const openid = query.openid;
    
    if (!openid) {
      res.writeHead(400, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Missing openid parameter' }));
      return;
    }
    
    console.log(`处理openid: ${openid}`);
    
    // 模拟处理时间
    setTimeout(() => {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ 
        success: true, 
        message: 'Request processed successfully',
        openid: openid,
        timestamp: new Date().toISOString()
      }));
      console.log(`已响应openid: ${openid}`);
    }, 100);
    
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

const PORT = 80;
const HOST = '127.0.0.1';

server.listen(PORT, HOST, () => {
  console.log(`测试API服务器运行在 http://${HOST}:${PORT}`);
  console.log('等待Chrome扩展的API请求...');
  console.log('API端点: GET /api/send?openid={tofakeid}');
});

server.on('error', (err) => {
  if (err.code === 'EACCES') {
    console.error(`错误: 无法绑定到端口 ${PORT}，可能需要管理员权限`);
    console.log('尝试使用其他端口，如8080:');
    console.log('修改background.js中的API地址为: http://127.0.0.1:8080/api/send');
  } else if (err.code === 'EADDRINUSE') {
    console.error(`错误: 端口 ${PORT} 已被占用`);
  } else {
    console.error('服务器错误:', err);
  }
});
