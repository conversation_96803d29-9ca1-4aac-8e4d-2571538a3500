# 快速修复指南 - API请求失败

## 🚀 立即解决"Failed to fetch"错误

### 步骤1: 重新加载扩展
1. 打开 `chrome://extensions/`
2. 找到"微信公众平台监控插件"
3. 点击刷新按钮重新加载扩展

### 步骤2: 启动测试API服务器
1. 打开命令行/终端
2. 进入扩展目录
3. 运行测试服务器：
```bash
node test-api-server.js
```

如果看到以下输出，说明服务器启动成功：
```
测试API服务器运行在 http://127.0.0.1:80
等待Chrome扩展的API请求...
API端点: GET /api/send?openid={tofakeid}
```

### 步骤3: 测试API连接
在浏览器中访问：
```
http://127.0.0.1/api/send?openid=test
```

应该看到类似以下的JSON响应：
```json
{
  "success": true,
  "message": "Request processed successfully",
  "openid": "test",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 步骤4: 测试扩展功能
1. 在微信公众平台触发图片下载
2. 查看扩展popup中的"API请求记录"
3. 应该看到成功的API请求记录

## ⚡ 常见问题快速解决

### 问题1: 端口80被占用
**错误信息：**
```
Error: EADDRINUSE: address already in use :::80
```

**解决方案：**
1. 修改 `test-api-server.js` 中的端口：
```javascript
const PORT = 8080; // 改为8080或其他端口
```

2. 修改 `background.js` 中的API地址：
```javascript
const apiUrl = `http://127.0.0.1:8080/api/send?openid=${tofakeid}`;
```

3. 更新 `manifest.json` 中的权限：
```json
"host_permissions": [
  "https://mp.weixin.qq.com/*",
  "http://127.0.0.1/*",
  "http://127.0.0.1:8080/*"
]
```

### 问题2: 权限不足
**错误信息：**
```
Error: EACCES: permission denied
```

**解决方案：**
- Windows: 以管理员身份运行命令行
- macOS/Linux: 使用 `sudo node test-api-server.js`
- 或者使用非特权端口（如8080）

### 问题3: 防火墙阻止
**解决方案：**
1. Windows防火墙：允许Node.js通过防火墙
2. 或临时关闭防火墙测试
3. 检查杀毒软件是否阻止连接

## 🔍 验证步骤

### 1. 检查扩展状态
- 扩展已启用 ✓
- 扩展已重新加载 ✓
- 权限配置正确 ✓

### 2. 检查API服务器
- 服务器已启动 ✓
- 端口可访问 ✓
- CORS配置正确 ✓

### 3. 检查网络连接
- 127.0.0.1可访问 ✓
- 防火墙允许连接 ✓
- 端口未被占用 ✓

## 📞 仍然有问题？

如果按照以上步骤仍然无法解决：

1. **查看详细日志**：
   - 打开Chrome开发者工具
   - 查看Console标签页的错误信息

2. **检查网络请求**：
   - 打开Network标签页
   - 查看是否有API请求发出

3. **使用curl测试**：
   ```bash
   curl -v "http://127.0.0.1/api/send?openid=test"
   ```

4. **查看完整故障排除指南**：
   - 阅读 `API_TROUBLESHOOTING.md` 文件

## 🎯 成功标志

当一切正常工作时，您应该看到：

1. **控制台日志**：
```
🌐 发送API请求: http://127.0.0.1/api/send?openid=xxx
📡 API响应状态: 200
✅ API请求成功
📄 API响应内容: {"success":true,...}
```

2. **扩展popup**：
- "API请求记录"区域显示绿色的成功记录
- 包含正确的tofakeid和响应内容

3. **API服务器日志**：
```
收到请求: GET /api/send?openid=xxx
处理openid: xxx
已响应openid: xxx
```

现在您的API集成功能应该正常工作了！
