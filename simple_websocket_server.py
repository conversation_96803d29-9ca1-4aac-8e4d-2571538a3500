#!/usr/bin/env python3
"""
简化的Python WebSocket服务器
更稳定的版本，解决启动后立即退出的问题
"""

import asyncio
import websockets
import json
import uuid
import logging
import threading
import time
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import socketserver

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ChromeExtensionServer:
    def __init__(self):
        self.chrome_extensions = set()
        self.pending_requests = {}
        
    async def register_chrome_extension(self, websocket):
        """注册Chrome扩展连接"""
        self.chrome_extensions.add(websocket)
        logger.info(f"🔌 Chrome扩展已连接，总连接数: {len(self.chrome_extensions)}")
        
        # 发送注册成功消息
        await websocket.send(json.dumps({
            'type': 'register_success',
            'message': 'Chrome扩展注册成功',
            'timestamp': datetime.now().isoformat()
        }))
        
    async def unregister_chrome_extension(self, websocket):
        """注销Chrome扩展连接"""
        self.chrome_extensions.discard(websocket)
        logger.info(f"🔌 Chrome扩展已断开，剩余连接数: {len(self.chrome_extensions)}")
        
    async def handle_websocket(self, websocket, path):
        """处理WebSocket连接"""
        logger.info(f"📱 新的WebSocket连接: {websocket.remote_address}")
        
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.process_websocket_message(websocket, data)
                except json.JSONDecodeError:
                    logger.error(f"❌ 无效的JSON消息: {message}")
                except Exception as e:
                    logger.error(f"❌ 处理WebSocket消息失败: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("🔌 WebSocket连接已关闭")
        except Exception as e:
            logger.error(f"❌ WebSocket连接错误: {e}")
        finally:
            await self.unregister_chrome_extension(websocket)
            
    async def process_websocket_message(self, websocket, data):
        """处理WebSocket消息"""
        message_type = data.get('type')
        logger.info(f"📡 收到WebSocket消息: {message_type}")
        
        if message_type == 'register':
            if data.get('client_type') == 'chrome_extension':
                await self.register_chrome_extension(websocket)
                
        elif message_type == 'execution_started':
            request_id = data.get('request_id')
            logger.info(f"🚀 Chrome扩展开始执行: {request_id}")
            
            if request_id in self.pending_requests:
                self.pending_requests[request_id]['status'] = 'executing'
                self.pending_requests[request_id]['started_at'] = datetime.now().isoformat()
                
        elif message_type == 'execution_complete':
            request_id = data.get('request_id')
            success = data.get('success', False)
            error = data.get('error')
            
            logger.info(f"✅ Chrome扩展执行完成: {request_id}, 成功: {success}")
            
            if request_id in self.pending_requests:
                self.pending_requests[request_id]['status'] = 'completed'
                self.pending_requests[request_id]['success'] = success
                self.pending_requests[request_id]['error'] = error
                self.pending_requests[request_id]['completed_at'] = datetime.now().isoformat()
                
        elif message_type == 'heartbeat':
            await websocket.send(json.dumps({
                'type': 'heartbeat_response',
                'timestamp': datetime.now().isoformat()
            }))
            
    async def trigger_click_sequence(self, data):
        """触发Chrome扩展执行点击序列"""
        if not self.chrome_extensions:
            return {
                'success': False,
                'error': '没有可用的Chrome扩展连接'
            }
            
        request_id = str(uuid.uuid4())
        
        self.pending_requests[request_id] = {
            'request_id': request_id,
            'data': data,
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'started_at': None,
            'completed_at': None,
            'success': None,
            'error': None
        }
        
        message = {
            'type': 'execute_click_sequence',
            'request_id': request_id,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
        
        sent_count = 0
        for extension in self.chrome_extensions.copy():
            try:
                await extension.send(json.dumps(message))
                sent_count += 1
                logger.info(f"📤 已发送触发消息到Chrome扩展: {request_id}")
            except Exception as e:
                logger.error(f"❌ 发送消息到Chrome扩展失败: {e}")
                self.chrome_extensions.discard(extension)
                
        if sent_count > 0:
            return {
                'success': True,
                'request_id': request_id,
                'sent_to': sent_count,
                'message': f'触发消息已发送到 {sent_count} 个Chrome扩展'
            }
        else:
            return {
                'success': False,
                'error': '发送触发消息失败'
            }
            
    def get_request_status(self, request_id):
        """获取请求状态"""
        return self.pending_requests.get(request_id, {
            'error': '请求ID不存在'
        })
        
    def get_server_status(self):
        """获取服务器状态"""
        return {
            'chrome_extensions_count': len(self.chrome_extensions),
            'pending_requests_count': len([r for r in self.pending_requests.values() if r['status'] == 'pending']),
            'total_requests_count': len(self.pending_requests),
            'server_time': datetime.now().isoformat()
        }

# 全局服务器实例
server = ChromeExtensionServer()

class HTTPRequestHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        """处理POST请求"""
        try:
            if self.path == '/trigger':
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
                
                logger.info(f"📥 收到HTTP触发请求: {data}")
                
                # 在新的事件循环中执行异步操作
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(server.trigger_click_sequence(data))
                loop.close()
                
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(result).encode('utf-8'))
                
            elif self.path == '/wait':
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length)
                data = json.loads(post_data.decode('utf-8'))
                
                request_id = data.get('request_id')
                timeout = data.get('timeout', 30)
                
                if not request_id:
                    self.send_error(400, '缺少request_id参数')
                    return
                    
                # 等待请求完成
                start_time = time.time()
                while time.time() - start_time < timeout:
                    status = server.get_request_status(request_id)
                    
                    if status.get('status') == 'completed':
                        result = {
                            'success': True,
                            'completed': True,
                            'result': status
                        }
                        break
                        
                    time.sleep(0.5)
                else:
                    result = {
                        'success': False,
                        'completed': False,
                        'error': '等待超时',
                        'timeout': timeout
                    }
                
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(result).encode('utf-8'))
                
            else:
                self.send_error(404, 'Not Found')
                
        except Exception as e:
            logger.error(f"❌ 处理POST请求失败: {e}")
            self.send_error(500, str(e))
            
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_url = urlparse(self.path)
            
            if parsed_url.path == '/status':
                query_params = parse_qs(parsed_url.query)
                request_id = query_params.get('request_id', [None])[0]
                
                if request_id:
                    result = server.get_request_status(request_id)
                else:
                    result = server.get_server_status()
                
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(result).encode('utf-8'))
                
            else:
                self.send_error(404, 'Not Found')
                
        except Exception as e:
            logger.error(f"❌ 处理GET请求失败: {e}")
            self.send_error(500, str(e))
            
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
    def log_message(self, format, *args):
        """禁用默认的HTTP日志"""
        pass

def start_http_server():
    """启动HTTP服务器"""
    try:
        httpd = HTTPServer(('127.0.0.1', 8080), HTTPRequestHandler)
        logger.info("🌐 HTTP服务器已启动: http://127.0.0.1:8080")
        httpd.serve_forever()
    except Exception as e:
        logger.error(f"❌ HTTP服务器启动失败: {e}")

async def start_websocket_server():
    """启动WebSocket服务器"""
    try:
        websocket_server = await websockets.serve(
            server.handle_websocket,
            "127.0.0.1",
            8765
        )
        logger.info("🔗 WebSocket服务器已启动: ws://127.0.0.1:8765")
        
        # 保持服务器运行
        await websocket_server.wait_closed()
        
    except Exception as e:
        logger.error(f"❌ WebSocket服务器启动失败: {e}")

def main():
    """主函数"""
    logger.info("🚀 启动Python WebSocket服务器...")
    
    # 在单独线程中启动HTTP服务器
    http_thread = threading.Thread(target=start_http_server, daemon=True)
    http_thread.start()
    
    logger.info("✅ 服务器启动成功")
    logger.info("🔗 WebSocket地址: ws://127.0.0.1:8765")
    logger.info("🌐 HTTP API地址: http://127.0.0.1:8080")
    logger.info("📡 等待Chrome扩展连接...")
    logger.info("🛑 按 Ctrl+C 停止服务器")
    
    # 启动WebSocket服务器
    try:
        asyncio.run(start_websocket_server())
    except KeyboardInterrupt:
        logger.info("🛑 收到停止信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"❌ 服务器运行错误: {e}")
    finally:
        logger.info("🛑 服务器已停止")

if __name__ == "__main__":
    main()
