# Python WebSocket集成指南

## 🎯 架构说明

```
PHP项目 → HTTP请求 → Python WebSocket服务器 → WebSocket → Chrome扩展
   ↑                        ↓                        ↓           ↓
   ←─── HTTP响应 ←─── 状态管理 ←─── WebSocket消息 ←─── 执行点击操作
```

### 工作流程
1. **PHP项目**发送HTTP请求到Python服务器
2. **Python服务器**通过WebSocket发送触发消息给Chrome扩展
3. **Chrome扩展**执行点击操作并发送完成确认
4. **Python服务器**返回执行结果给PHP项目

## 🚀 快速开始

### 1. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 2. 启动Python WebSocket服务器
```bash
python websocket_server.py
```

服务器启动后会显示：
```
✅ 服务器启动成功
🔗 WebSocket地址: ws://127.0.0.1:8765
🌐 HTTP API地址: http://127.0.0.1:8080
📡 等待Chrome扩展连接...
```

### 3. 重新加载Chrome扩展
确保扩展已安装并重新加载以连接到Python WebSocket服务器。

### 4. 测试连接
```bash
python python_client_example.py
```

## 📡 HTTP API接口

### POST /trigger
触发Chrome扩展执行点击操作

**请求示例**:
```bash
curl -X POST http://127.0.0.1:8080/trigger \
  -H "Content-Type: application/json" \
  -d '{"user_id": 123, "action": "process_user"}'
```

**响应示例**:
```json
{
  "success": true,
  "request_id": "12345678-1234-1234-1234-123456789abc",
  "sent_to": 1,
  "message": "触发消息已发送到 1 个Chrome扩展"
}
```

### GET /status
查询服务器或请求状态

**查询服务器状态**:
```bash
curl http://127.0.0.1:8080/status
```

**查询特定请求状态**:
```bash
curl "http://127.0.0.1:8080/status?request_id=12345678-1234-1234-1234-123456789abc"
```

### POST /wait
等待请求执行完成

**请求示例**:
```bash
curl -X POST http://127.0.0.1:8080/wait \
  -H "Content-Type: application/json" \
  -d '{"request_id": "12345678-1234-1234-1234-123456789abc", "timeout": 30}'
```

## 💻 PHP调用示例

### 基本触发
```php
<?php
function triggerChromeExtension($data) {
    $url = 'http://127.0.0.1:8080/trigger';
    
    $postData = json_encode($data);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $postData
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    return json_decode($response, true);
}

// 使用示例
$result = triggerChromeExtension([
    'user_id' => 123,
    'action' => 'process_user'
]);

if ($result['success']) {
    echo "触发成功！请求ID: " . $result['request_id'];
}
?>
```

### 触发并等待完成
```php
<?php
function triggerAndWait($data, $timeout = 30) {
    // 先触发
    $triggerResult = triggerChromeExtension($data);
    if (!$triggerResult['success']) {
        return $triggerResult;
    }
    
    $requestId = $triggerResult['request_id'];
    
    // 等待完成
    $waitUrl = 'http://127.0.0.1:8080/wait';
    $waitData = json_encode([
        'request_id' => $requestId,
        'timeout' => $timeout
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $waitData
        ]
    ]);
    
    $response = file_get_contents($waitUrl, false, $context);
    return json_decode($response, true);
}

// 使用示例
$result = triggerAndWait([
    'order_id' => 'ORDER-2024-001',
    'priority' => 'high'
], 30);

if ($result['completed'] && $result['result']['success']) {
    echo "执行完成！";
} else {
    echo "执行失败: " . $result['error'];
}
?>
```

### 检查状态
```php
<?php
function getServerStatus() {
    $response = file_get_contents('http://127.0.0.1:8080/status');
    return json_decode($response, true);
}

function getRequestStatus($requestId) {
    $url = "http://127.0.0.1:8080/status?request_id=" . urlencode($requestId);
    $response = file_get_contents($url);
    return json_decode($response, true);
}

// 使用示例
$serverStatus = getServerStatus();
echo "Chrome扩展连接数: " . $serverStatus['chrome_extensions_count'];
?>
```

## 🎯 实际应用场景

### 用户注册后触发
```php
function onUserRegistered($userId, $userData) {
    $result = triggerChromeExtension([
        'event' => 'user_registered',
        'user_id' => $userId,
        'user_data' => $userData,
        'timestamp' => time()
    ]);
    
    if ($result['success']) {
        error_log("Chrome extension triggered for user: $userId");
        return true;
    } else {
        error_log("Failed to trigger Chrome extension: " . $result['error']);
        return false;
    }
}
```

### 订单处理流程
```php
function processOrder($orderId) {
    $result = triggerAndWait([
        'event' => 'order_processing',
        'order_id' => $orderId,
        'priority' => 'high'
    ], 60);
    
    if ($result['completed'] && $result['result']['success']) {
        echo "订单处理完成，Chrome扩展操作已执行\n";
        return true;
    } else {
        echo "Chrome扩展操作失败\n";
        return false;
    }
}
```

### 定时任务集成
```php
// cron任务
function dailyMaintenance() {
    // 先检查服务器状态
    $status = getServerStatus();
    if ($status['chrome_extensions_count'] == 0) {
        error_log("No Chrome extensions connected");
        return false;
    }
    
    $result = triggerChromeExtension([
        'event' => 'daily_maintenance',
        'scheduled_time' => date('Y-m-d H:i:s'),
        'task_type' => 'automated'
    ]);
    
    return $result['success'];
}
```

## 🔍 监控和调试

### Python服务器日志
服务器会输出详细的日志信息：
```
2024-01-01 12:00:00,123 - INFO - 🔌 Chrome扩展已连接，总连接数: 1
2024-01-01 12:00:05,456 - INFO - 📥 收到PHP触发请求: {'user_id': 123}
2024-01-01 12:00:06,789 - INFO - 📤 已发送触发消息到Chrome扩展: abc-123
2024-01-01 12:00:07,012 - INFO - ✅ Chrome扩展执行完成: abc-123, 成功: True
```

### Chrome扩展日志
打开Chrome开发者工具Console查看：
```
✅ WebSocket连接已建立
📡 收到Python WebSocket消息: {type: "execute_click_sequence", ...}
🎯 收到Python服务器的触发请求
🚀 开始执行Python服务器触发的点击序列
✅ 点击序列执行成功
```

### 状态监控
```bash
# 检查服务器状态
curl http://127.0.0.1:8080/status

# 检查特定请求
curl "http://127.0.0.1:8080/status?request_id=YOUR_REQUEST_ID"
```

## ⚠️ 注意事项

### 安全考虑
1. **访问控制**: 在生产环境中添加身份验证
2. **HTTPS**: 使用HTTPS和WSS保护数据传输
3. **输入验证**: 验证所有HTTP请求数据

### 性能优化
1. **连接管理**: 保持WebSocket连接稳定
2. **请求限制**: 避免过于频繁的触发请求
3. **超时设置**: 设置合理的等待超时时间

### 部署建议
1. **进程管理**: 使用systemd或supervisor管理Python进程
2. **负载均衡**: 多实例部署时考虑负载均衡
3. **监控**: 添加健康检查和性能监控

## 🔄 故障排除

### 常见问题

#### 1. Python服务器启动失败
```bash
# 检查端口是否被占用
netstat -an | grep 8080
netstat -an | grep 8765

# 检查Python依赖
pip list | grep websockets
pip list | grep aiohttp
```

#### 2. Chrome扩展无法连接
- 检查扩展是否已重新加载
- 确认WebSocket地址正确（ws://127.0.0.1:8765）
- 查看扩展Console错误信息

#### 3. PHP请求失败
- 检查Python服务器是否运行
- 确认HTTP API地址正确（http://127.0.0.1:8080）
- 检查网络连接和防火墙

### 调试步骤
1. 启动Python服务器并查看日志
2. 重新加载Chrome扩展
3. 运行Python测试客户端
4. 检查所有组件的日志输出

现在您可以通过Python WebSocket服务器实现PHP项目和Chrome扩展之间的高效通信！
