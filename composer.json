{"name": "chrome-extension/websocket-integration", "description": "WebSocket integration for Chrome Extension and PHP projects", "type": "project", "require": {"php": ">=7.4", "ratchet/pawl": "^0.4", "textalk/websocket-client": "^1.4"}, "autoload": {"psr-4": {"ChromeExtension\\": "src/"}}, "scripts": {"start-server": "php websocket-server.php", "test-client": "php php-websocket-client.php"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}