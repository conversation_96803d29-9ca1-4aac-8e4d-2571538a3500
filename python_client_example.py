#!/usr/bin/env python3
"""
Python客户端示例
展示如何调用WebSocket服务器触发Chrome扩展
"""

import requests
import json
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChromeExtensionClient:
    def __init__(self, server_url="http://127.0.0.1:8080"):
        self.server_url = server_url.rstrip('/')
        
    def trigger_click(self, data=None):
        """触发Chrome扩展执行点击操作"""
        if data is None:
            data = {}
            
        try:
            response = requests.post(
                f"{self.server_url}/trigger",
                json=data,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 触发成功: {result}")
                return result
            else:
                logger.error(f"❌ 触发失败: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}"
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 请求失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
            
    def get_status(self, request_id=None):
        """获取状态"""
        try:
            params = {}
            if request_id:
                params['request_id'] = request_id
                
            response = requests.get(
                f"{self.server_url}/status",
                params=params,
                timeout=5
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}"
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': str(e)
            }
            
    def wait_for_completion(self, request_id, timeout=30):
        """等待执行完成"""
        try:
            response = requests.post(
                f"{self.server_url}/wait",
                json={
                    'request_id': request_id,
                    'timeout': timeout
                },
                timeout=timeout + 5
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    'success': False,
                    'error': f"HTTP {response.status_code}: {response.text}"
                }
                
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': str(e)
            }
            
    def trigger_and_wait(self, data=None, timeout=30):
        """触发并等待完成"""
        # 先触发
        trigger_result = self.trigger_click(data)
        if not trigger_result.get('success'):
            return trigger_result
            
        request_id = trigger_result.get('request_id')
        if not request_id:
            return {
                'success': False,
                'error': '未获取到request_id'
            }
            
        logger.info(f"⏳ 等待执行完成: {request_id}")
        
        # 等待完成
        wait_result = self.wait_for_completion(request_id, timeout)
        
        if wait_result.get('completed'):
            result_data = wait_result.get('result', {})
            return {
                'success': result_data.get('success', False),
                'request_id': request_id,
                'result': result_data,
                'error': result_data.get('error')
            }
        else:
            return wait_result

def main():
    """主函数 - 演示如何使用"""
    print("🚀 Chrome扩展Python客户端测试")
    print("=" * 40)
    
    client = ChromeExtensionClient()
    
    # 检查服务器状态
    print("\n📊 检查服务器状态...")
    status = client.get_status()
    if status.get('chrome_extensions_count', 0) > 0:
        print(f"✅ 服务器在线，Chrome扩展连接数: {status['chrome_extensions_count']}")
    else:
        print("❌ 没有Chrome扩展连接")
        print("💡 请确保:")
        print("   1. Python WebSocket服务器已启动: python websocket_server.py")
        print("   2. Chrome扩展已安装并重新加载")
        return
        
    # 示例1: 简单触发
    print("\n📝 示例1: 简单触发")
    print("-" * 20)
    result = client.trigger_click({
        'action': 'test_trigger',
        'user_id': 123,
        'timestamp': time.time()
    })
    
    if result.get('success'):
        print(f"✅ 触发成功！请求ID: {result['request_id']}")
        print(f"📤 发送到: {result['sent_to']} 个扩展")
    else:
        print(f"❌ 触发失败: {result.get('error')}")
        
    # 示例2: 触发并等待完成
    print("\n📝 示例2: 触发并等待完成")
    print("-" * 25)
    result = client.trigger_and_wait({
        'action': 'process_order',
        'order_id': 'ORDER-2024-001',
        'priority': 'high'
    }, timeout=30)
    
    if result.get('success'):
        print("🎉 执行完成！")
        print(f"📊 请求ID: {result['request_id']}")
        result_data = result.get('result', {})
        print(f"⏰ 创建时间: {result_data.get('created_at')}")
        print(f"🚀 开始时间: {result_data.get('started_at')}")
        print(f"✅ 完成时间: {result_data.get('completed_at')}")
    else:
        print(f"❌ 执行失败: {result.get('error')}")
        
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
