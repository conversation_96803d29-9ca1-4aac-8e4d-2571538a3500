# PHP触发服务使用指南

## 🎯 功能概述

通过PHP触发服务，您可以从任何PHP脚本远程触发Chrome扩展执行点击序列操作。这个服务提供了一个HTTP API接口，让PHP应用程序能够与Chrome扩展进行通信。

## 🏗️ 架构说明

```
PHP应用 → PHP触发服务器 → Chrome扩展 → 网页操作
   ↑                                      ↓
   ←─────── 执行确认 ←─────── 执行完成 ←─────
```

### 组件说明
1. **PHP触发服务器** (`php-trigger-server.php`): HTTP服务器，处理触发请求
2. **Chrome扩展**: 监听服务器请求，执行页面操作
3. **PHP客户端** (`php-client-example.php`): 示例客户端，展示如何使用

## 🚀 快速开始

### 1. 启动PHP触发服务器
```bash
php -S 127.0.0.1:8888 php-trigger-server.php
```

### 2. 重新加载Chrome扩展
确保扩展已安装并重新加载以启用PHP监听功能。

### 3. 测试连接
在浏览器中访问: `http://127.0.0.1:8888`

### 4. 触发点击序列
```bash
# 命令行测试
php php-client-example.php

# 或访问网页
http://127.0.0.1:8888/trigger
```

## 📡 API接口

### GET /check-trigger
Chrome扩展用于检查是否有触发请求

**响应示例:**
```json
{
  "trigger": true,
  "last_trigger_time": "2024-01-01 12:00:00",
  "trigger_count": 5,
  "trigger_data": {
    "source": "manual",
    "ip": "127.0.0.1"
  }
}
```

### POST /confirm-trigger
Chrome扩展用于确认执行完成

**请求体:**
```json
{
  "status": "executed",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### GET /trigger
触发点击序列执行

**响应示例:**
```json
{
  "success": true,
  "message": "点击序列已触发",
  "status": {
    "trigger": true,
    "trigger_count": 6
  }
}
```

### GET /status
查看当前状态

### GET /clear
清空所有状态

## 💻 PHP客户端使用

### 基本使用
```php
<?php
require_once 'php-client-example.php';

$trigger = new ChromeExtensionTrigger();

// 触发点击序列
$result = $trigger->triggerClickSequence();
if ($result['success']) {
    echo "触发成功！\n";
} else {
    echo "触发失败: " . $result['error'] . "\n";
}
?>
```

### 触发并等待完成
```php
<?php
$trigger = new ChromeExtensionTrigger();

// 触发并等待执行完成（最多等待30秒）
$result = $trigger->triggerAndWait(30);
if ($result['success']) {
    echo "执行完成！\n";
} else {
    echo "执行失败: " . $result['error'] . "\n";
}
?>
```

### 检查状态
```php
<?php
$trigger = new ChromeExtensionTrigger();

$status = $trigger->getStatus();
if ($status['success']) {
    echo "触发次数: " . $status['trigger_count'] . "\n";
    echo "最后触发: " . $status['last_trigger_time'] . "\n";
}
?>
```

## 🔧 工作流程

### 1. 触发流程
1. PHP脚本调用 `/trigger` 接口
2. 服务器设置 `trigger=true` 状态
3. Chrome扩展每2秒检查 `/check-trigger`
4. 扩展发现触发请求，执行点击序列
5. 扩展调用 `/confirm-trigger` 确认完成
6. 服务器设置 `trigger=false` 状态

### 2. 监听机制
- Chrome扩展每2秒轮询服务器
- 发现触发请求时立即执行
- 执行完成后发送确认

### 3. 状态管理
- 使用JSON文件存储状态
- 支持多次触发
- 记录详细的时间戳

## 🛠️ 配置选项

### 修改监听端口
在 `background.js` 中修改:
```javascript
const response = await fetch('http://127.0.0.1:8080/check-trigger', {
```

在 `php-trigger-server.php` 中启动:
```bash
php -S 127.0.0.1:8080 php-trigger-server.php
```

### 修改检查频率
在 `background.js` 中修改:
```javascript
}, 5000); // 改为每5秒检查一次
```

### 自定义触发数据
```php
$result = $trigger->triggerClickSequence([
    'user_id' => 123,
    'action' => 'custom_action'
]);
```

## 🔍 调试和监控

### 查看日志
1. **Chrome扩展日志**: 打开开发者工具Console
2. **PHP服务器日志**: 查看命令行输出
3. **状态文件**: 查看 `trigger_status.json`

### 常见问题

#### 1. 扩展无法连接服务器
- 检查服务器是否启动
- 检查端口是否被占用
- 检查防火墙设置

#### 2. 触发无响应
- 检查扩展是否已重新加载
- 查看扩展Console是否有错误
- 确认目标页面已打开

#### 3. 权限错误
- 确保manifest.json包含正确的host_permissions
- 重新加载扩展

## 📊 性能考虑

### 轮询频率
- 默认2秒轮询一次
- 可根据需要调整频率
- 频率过高可能影响性能

### 并发处理
- 当前版本不支持并发触发
- 新触发会覆盖之前的状态
- 建议等待前一个执行完成

### 资源使用
- 轮询会产生少量网络请求
- JSON状态文件占用很少磁盘空间
- 对系统性能影响极小

## 🔄 扩展应用

### 集成到现有系统
```php
// 在您的业务逻辑中
function processUserAction($userId) {
    // 业务处理...
    
    // 触发Chrome扩展操作
    $trigger = new ChromeExtensionTrigger();
    $result = $trigger->triggerClickSequence([
        'user_id' => $userId,
        'action' => 'process_user'
    ]);
    
    if ($result['success']) {
        // 记录成功日志
        log("Chrome扩展操作已触发: $userId");
    }
}
```

### 定时任务
```php
// cron任务示例
$trigger = new ChromeExtensionTrigger();

// 每小时触发一次
if (date('i') == '0') {
    $trigger->triggerAndWait();
}
```

### Web界面集成
```php
// 在Web管理界面中添加触发按钮
if ($_POST['action'] == 'trigger_chrome') {
    $trigger = new ChromeExtensionTrigger();
    $result = $trigger->triggerAndWait();
    
    if ($result['success']) {
        $message = "操作执行成功！";
    } else {
        $message = "操作失败: " . $result['error'];
    }
}
```

现在您可以通过PHP脚本远程控制Chrome扩展执行点击操作了！
