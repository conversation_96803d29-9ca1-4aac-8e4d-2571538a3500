#!/usr/bin/env python3
"""
测试服务器 - 最简单的版本
用于验证基本功能
"""

import asyncio
import websockets
import json
import uuid
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局变量
chrome_extensions = set()
pending_requests = {}

async def handle_websocket(websocket, path):
    """处理WebSocket连接"""
    logger.info(f"📱 新的WebSocket连接: {websocket.remote_address}")
    
    try:
        # 注册Chrome扩展
        chrome_extensions.add(websocket)
        logger.info(f"🔌 Chrome扩展已连接，总连接数: {len(chrome_extensions)}")
        
        # 发送注册成功消息
        await websocket.send(json.dumps({
            'type': 'register_success',
            'message': 'Chrome扩展注册成功',
            'timestamp': datetime.now().isoformat()
        }))
        
        # 处理消息
        async for message in websocket:
            try:
                data = json.loads(message)
                message_type = data.get('type')
                logger.info(f"📡 收到WebSocket消息: {message_type}")
                
                if message_type == 'register':
                    logger.info("✅ Chrome扩展注册确认")
                    
                elif message_type == 'execution_started':
                    request_id = data.get('request_id')
                    logger.info(f"🚀 Chrome扩展开始执行: {request_id}")
                    
                    if request_id in pending_requests:
                        pending_requests[request_id]['status'] = 'executing'
                        pending_requests[request_id]['started_at'] = datetime.now().isoformat()
                        
                elif message_type == 'execution_complete':
                    request_id = data.get('request_id')
                    success = data.get('success', False)
                    error = data.get('error')
                    
                    logger.info(f"✅ Chrome扩展执行完成: {request_id}, 成功: {success}")
                    
                    if request_id in pending_requests:
                        pending_requests[request_id]['status'] = 'completed'
                        pending_requests[request_id]['success'] = success
                        pending_requests[request_id]['error'] = error
                        pending_requests[request_id]['completed_at'] = datetime.now().isoformat()
                        
                elif message_type == 'heartbeat':
                    await websocket.send(json.dumps({
                        'type': 'heartbeat_response',
                        'timestamp': datetime.now().isoformat()
                    }))
                    
            except json.JSONDecodeError:
                logger.error(f"❌ 无效的JSON消息: {message}")
            except Exception as e:
                logger.error(f"❌ 处理WebSocket消息失败: {e}")
                
    except websockets.exceptions.ConnectionClosed:
        logger.info("🔌 WebSocket连接已关闭")
    except Exception as e:
        logger.error(f"❌ WebSocket连接错误: {e}")
    finally:
        # 注销Chrome扩展
        chrome_extensions.discard(websocket)
        logger.info(f"🔌 Chrome扩展已断开，剩余连接数: {len(chrome_extensions)}")

async def trigger_click_sequence(data):
    """触发Chrome扩展执行点击序列"""
    if not chrome_extensions:
        return {
            'success': False,
            'error': '没有可用的Chrome扩展连接'
        }
        
    request_id = str(uuid.uuid4())
    
    pending_requests[request_id] = {
        'request_id': request_id,
        'data': data,
        'status': 'pending',
        'created_at': datetime.now().isoformat(),
        'started_at': None,
        'completed_at': None,
        'success': None,
        'error': None
    }
    
    message = {
        'type': 'execute_click_sequence',
        'request_id': request_id,
        'data': data,
        'timestamp': datetime.now().isoformat()
    }
    
    sent_count = 0
    for extension in chrome_extensions.copy():
        try:
            await extension.send(json.dumps(message))
            sent_count += 1
            logger.info(f"📤 已发送触发消息到Chrome扩展: {request_id}")
        except Exception as e:
            logger.error(f"❌ 发送消息到Chrome扩展失败: {e}")
            chrome_extensions.discard(extension)
            
    if sent_count > 0:
        return {
            'success': True,
            'request_id': request_id,
            'sent_to': sent_count,
            'message': f'触发消息已发送到 {sent_count} 个Chrome扩展'
        }
    else:
        return {
            'success': False,
            'error': '发送触发消息失败'
        }

async def test_trigger():
    """测试触发功能"""
    while True:
        try:
            await asyncio.sleep(10)  # 每10秒测试一次
            
            if chrome_extensions:
                logger.info("🧪 执行测试触发...")
                result = await trigger_click_sequence({
                    'test': True,
                    'timestamp': datetime.now().isoformat()
                })
                logger.info(f"🧪 测试结果: {result}")
            else:
                logger.info("⏳ 等待Chrome扩展连接...")
                
        except Exception as e:
            logger.error(f"❌ 测试触发失败: {e}")

async def main():
    """主函数"""
    logger.info("🚀 启动测试WebSocket服务器...")
    
    # 启动WebSocket服务器
    websocket_server = await websockets.serve(
        handle_websocket,
        "127.0.0.1",
        8765
    )
    
    logger.info("✅ 服务器启动成功")
    logger.info("🔗 WebSocket地址: ws://127.0.0.1:8765")
    logger.info("📡 等待Chrome扩展连接...")
    logger.info("🧪 将每10秒自动测试一次触发功能")
    logger.info("🛑 按 Ctrl+C 停止服务器")
    
    # 启动测试任务
    test_task = asyncio.create_task(test_trigger())
    
    try:
        # 等待服务器关闭
        await websocket_server.wait_closed()
    except KeyboardInterrupt:
        logger.info("🛑 收到停止信号")
        test_task.cancel()
    finally:
        websocket_server.close()
        await websocket_server.wait_closed()
        logger.info("🛑 服务器已停止")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 程序已退出")
