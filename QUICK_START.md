# 🚀 快速开始指南

## 方案选择

根据您的环境选择合适的方案：

### 方案1: 无依赖WebSocket（推荐）
✅ **优点**: 无需安装任何依赖，开箱即用  
❌ **缺点**: 性能略低于专业WebSocket库

### 方案2: 完整WebSocket（高性能）
✅ **优点**: 高性能，功能完整  
❌ **缺点**: 需要安装Composer依赖

### 方案3: HTTP轮询（兼容性最好）
✅ **优点**: 兼容性最好，无特殊要求  
❌ **缺点**: 有延迟，效率较低

---

## 🎯 方案1: 无依赖WebSocket（推荐）

### 1. 启动WebSocket服务器
```bash
# 如果已安装Ratchet
php simple-websocket-server.php

# 如果没有安装依赖，会显示提示信息
```

### 2. 安装Ratchet（如果需要）
```bash
composer require ratchet/pawl
```

### 3. 重新加载Chrome扩展
在Chrome扩展管理页面重新加载扩展

### 4. 在PHP项目中使用
```php
<?php
require_once 'ChromeExtensionTriggerNoDeps.php';

$trigger = new ChromeExtensionTriggerNoDeps();

// 简单触发
$result = $trigger->triggerClick(['user_id' => 123]);
if ($result['success']) {
    echo "触发成功！";
}

// 触发并等待完成
$result = $trigger->triggerAndWait(['order_id' => 'ORDER-001'], 30);
if ($result['success']) {
    echo "执行完成！";
}
?>
```

### 5. 测试
```bash
# 命令行测试
php ChromeExtensionTriggerNoDeps.php

# 或在浏览器中访问
http://localhost/path/to/ChromeExtensionTriggerNoDeps.php
```

---

## 🔧 方案2: 完整WebSocket

### 1. 安装依赖
```bash
composer install
```

如果遇到包版本问题：
```bash
composer require ratchet/pawl
```

### 2. 启动WebSocket服务器
```bash
php websocket-server.php
```

### 3. 使用完整功能
```php
<?php
require_once 'ChromeExtensionTrigger.php';

$trigger = new ChromeExtensionTrigger();
$result = $trigger->triggerAndWait(['test' => true], 30);
?>
```

---

## 🌐 方案3: HTTP轮询

### 1. 启动HTTP服务器
```bash
php -S 127.0.0.1:9999 php-project-example.php
```

### 2. 使用HTTP API
```php
<?php
// 简单的HTTP请求
$response = file_get_contents('http://127.0.0.1:9999/api/trigger-chrome-click', false, 
    stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => json_encode(['user_id' => 123])
        ]
    ])
);

$result = json_decode($response, true);
if ($result['success']) {
    echo "触发成功！";
}
?>
```

---

## 🔍 故障排除

### 问题1: Composer依赖安装失败
**解决方案**: 使用无依赖版本
```bash
php ChromeExtensionTriggerNoDeps.php
```

### 问题2: WebSocket连接失败
**检查项目**:
1. 服务器是否启动
2. 端口9998是否被占用
3. 防火墙设置

**解决方案**:
```bash
# 检查端口
netstat -an | grep 9998

# 更换端口（修改代码中的9998为其他端口）
```

### 问题3: Chrome扩展无响应
**检查项目**:
1. 扩展是否已重新加载
2. 目标网页是否已打开
3. 扩展Console是否有错误

**解决方案**:
1. 重新加载扩展
2. 打开开发者工具查看Console
3. 确认网页URL匹配

### 问题4: PHP版本兼容性
**要求**: PHP 7.4+

**检查版本**:
```bash
php -v
```

---

## 📊 性能对比

| 方案 | 延迟 | 资源占用 | 安装难度 | 推荐度 |
|------|------|----------|----------|--------|
| 无依赖WebSocket | ~100ms | 低 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 完整WebSocket | ~50ms | 低 | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| HTTP轮询 | 3秒+ | 中 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

---

## 🎯 推荐使用流程

### 开发阶段
1. 使用**无依赖WebSocket**快速开始
2. 测试基本功能
3. 根据需要升级到完整版本

### 生产环境
1. 使用**完整WebSocket**获得最佳性能
2. 配置进程管理（如Supervisor）
3. 添加监控和日志

---

## 📝 示例代码

### 用户注册触发
```php
function onUserRegistered($userId, $userData) {
    $trigger = new ChromeExtensionTriggerNoDeps();
    
    $result = $trigger->triggerClick([
        'event' => 'user_registered',
        'user_id' => $userId,
        'user_data' => $userData
    ]);
    
    if ($result['success']) {
        error_log("Chrome extension triggered for user: $userId");
        return true;
    } else {
        error_log("Failed to trigger Chrome extension: " . $result['error']);
        return false;
    }
}
```

### 订单处理触发
```php
function processOrder($orderId) {
    $trigger = new ChromeExtensionTriggerNoDeps();
    
    $result = $trigger->triggerAndWait([
        'event' => 'order_processing',
        'order_id' => $orderId,
        'priority' => 'high'
    ], 60);
    
    return $result['success'];
}
```

### 定时任务触发
```php
// cron任务
function dailyMaintenance() {
    $trigger = new ChromeExtensionTriggerNoDeps();
    
    if (!$trigger->testConnection()) {
        error_log("WebSocket server not available");
        return false;
    }
    
    $result = $trigger->triggerClick([
        'event' => 'daily_maintenance',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
    return $result['success'];
}
```

---

## 🎉 开始使用

选择适合您的方案，按照上述步骤操作即可开始使用Chrome扩展的远程触发功能！

如果遇到问题，请查看详细文档：
- `WEBSOCKET_INTEGRATION_GUIDE.md` - WebSocket详细指南
- `PHP_INTEGRATION_GUIDE.md` - HTTP轮询指南
