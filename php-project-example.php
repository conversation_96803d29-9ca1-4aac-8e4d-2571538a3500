<?php
/**
 * PHP项目端接口示例
 * 用于与Chrome扩展通信，接收扩展的轮询请求并发送触发指令
 * 
 * 部署方法:
 * 1. 将此文件放在您的PHP项目中
 * 2. 启动Web服务器: php -S 127.0.0.1:9999 php-project-example.php
 * 3. Chrome扩展会自动轮询检查触发指令
 */

// 设置CORS头部，允许Chrome扩展访问
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, Accept, User-Agent');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 获取请求路径和方法
$requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$method = $_SERVER['REQUEST_METHOD'];

// 日志函数
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message\n";
    error_log("[$timestamp] Chrome Extension API: $message");
}

// 触发状态文件路径
$statusFile = __DIR__ . '/chrome_extension_status.json';

// 初始化状态文件
function initStatus() {
    global $statusFile;
    if (!file_exists($statusFile)) {
        $defaultStatus = [
            'action' => 'none',
            'execute' => false,
            'last_trigger_time' => null,
            'last_check_time' => null,
            'last_confirm_time' => null,
            'trigger_count' => 0,
            'data' => null
        ];
        file_put_contents($statusFile, json_encode($defaultStatus, JSON_PRETTY_PRINT));
    }
}

// 读取状态
function getStatus() {
    global $statusFile;
    initStatus();
    $content = file_get_contents($statusFile);
    return json_decode($content, true);
}

// 更新状态
function updateStatus($updates) {
    global $statusFile;
    $current = getStatus();
    $updated = array_merge($current, $updates);
    file_put_contents($statusFile, json_encode($updated, JSON_PRETTY_PRINT));
    return $updated;
}

// 路由处理
switch ($requestUri) {
    case '/':
        // 主页 - 管理界面
        header('Content-Type: text/html; charset=utf-8');
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Chrome扩展通信接口</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .status { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
                .endpoint { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007cba; }
                .method { color: #007cba; font-weight: bold; }
                .url { color: #d63384; font-family: monospace; }
                button { padding: 12px 24px; margin: 8px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
                button:hover { background: #005a87; }
                button.danger { background: #dc3545; }
                button.danger:hover { background: #c82333; }
                .result { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; white-space: pre-wrap; font-family: monospace; font-size: 12px; }
                .code { background: #f1f3f4; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 Chrome扩展通信接口</h1>
                <p>此接口用于PHP项目与Chrome扩展通信，触发扩展执行点击操作。</p>
                
                <div class="status">
                    <h3>📊 当前状态</h3>
                    <div id="currentStatus">加载中...</div>
                </div>
                
                <h2>🎮 快速操作</h2>
                <button onclick="triggerClick()">🎯 触发点击序列</button>
                <button onclick="checkStatus()">📊 检查状态</button>
                <button onclick="clearStatus()" class="danger">🗑️ 清空状态</button>
                
                <div id="result" class="result" style="display:none;"></div>
                
                <h2>📡 API端点</h2>
                <div class="endpoint">
                    <span class="method">GET</span> <span class="url">/chrome-extension/check</span><br>
                    Chrome扩展轮询此端点检查是否有触发指令
                </div>
                <div class="endpoint">
                    <span class="method">POST</span> <span class="url">/chrome-extension/confirm</span><br>
                    Chrome扩展向此端点发送执行确认
                </div>
                <div class="endpoint">
                    <span class="method">POST</span> <span class="url">/api/trigger-chrome-click</span><br>
                    您的PHP代码调用此端点触发Chrome扩展
                </div>
                
                <h2>💻 PHP调用示例</h2>
                <div class="code">// 在您的PHP项目中调用
$response = file_get_contents('http://127.0.0.1:9999/api/trigger-chrome-click', false, stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => 'Content-Type: application/json',
        'content' => json_encode(['user_id' => 123, 'action' => 'process_user'])
    ]
]));

$result = json_decode($response, true);
if ($result['success']) {
    echo "Chrome扩展触发成功！";
}</div>
            </div>
            
            <script>
                async function request(url, options = {}) {
                    try {
                        const response = await fetch(url, options);
                        const data = await response.json();
                        return data;
                    } catch (error) {
                        return { error: error.message };
                    }
                }
                
                async function triggerClick() {
                    showResult('🚀 正在触发Chrome扩展...');
                    const result = await request('/api/trigger-chrome-click', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ source: 'web_interface', timestamp: new Date().toISOString() })
                    });
                    showResult('触发结果:\n' + JSON.stringify(result, null, 2));
                    updateStatus();
                }
                
                async function checkStatus() {
                    showResult('📊 检查状态中...');
                    const result = await request('/api/status');
                    showResult('当前状态:\n' + JSON.stringify(result, null, 2));
                    updateStatus();
                }
                
                async function clearStatus() {
                    showResult('🗑️ 清空状态中...');
                    const result = await request('/api/clear', { method: 'POST' });
                    showResult('清空结果:\n' + JSON.stringify(result, null, 2));
                    updateStatus();
                }
                
                function showResult(text) {
                    const resultDiv = document.getElementById('result');
                    resultDiv.textContent = text;
                    resultDiv.style.display = 'block';
                }
                
                async function updateStatus() {
                    const status = await request('/api/status');
                    const statusDiv = document.getElementById('currentStatus');
                    statusDiv.innerHTML = `
                        <strong>执行状态:</strong> ${status.execute ? '等待执行' : '空闲'}<br>
                        <strong>触发次数:</strong> ${status.trigger_count || 0}<br>
                        <strong>最后触发:</strong> ${status.last_trigger_time || '无'}<br>
                        <strong>最后确认:</strong> ${status.last_confirm_time || '无'}
                    `;
                }
                
                // 页面加载时更新状态
                updateStatus();
                
                // 每5秒自动更新状态
                setInterval(updateStatus, 5000);
            </script>
        </body>
        </html>
        <?php
        break;
        
    case '/chrome-extension/check':
        // Chrome扩展检查端点
        if ($method === 'GET') {
            $status = updateStatus(['last_check_time' => date('Y-m-d H:i:s')]);
            logMessage("Chrome扩展检查状态: " . ($status['execute'] ? 'true' : 'false'));
            echo json_encode($status);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;
        
    case '/chrome-extension/confirm':
        // Chrome扩展确认端点
        if ($method === 'POST') {
            $input = json_decode(file_get_contents('php://input'), true);
            $status = updateStatus([
                'execute' => false,
                'action' => 'none',
                'last_confirm_time' => date('Y-m-d H:i:s'),
                'confirm_data' => $input
            ]);
            logMessage("Chrome扩展确认执行完成");
            echo json_encode(['success' => true, 'message' => '确认已接收', 'status' => $status]);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;
        
    case '/api/trigger-chrome-click':
        // PHP项目调用的触发端点
        if ($method === 'POST') {
            $input = json_decode(file_get_contents('php://input'), true);
            $status = updateStatus([
                'action' => 'trigger_click',
                'execute' => true,
                'last_trigger_time' => date('Y-m-d H:i:s'),
                'trigger_count' => getStatus()['trigger_count'] + 1,
                'data' => $input
            ]);
            logMessage("PHP项目触发Chrome扩展点击 #" . $status['trigger_count']);
            echo json_encode([
                'success' => true, 
                'message' => 'Chrome扩展触发指令已设置',
                'trigger_id' => $status['trigger_count'],
                'status' => $status
            ]);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;
        
    case '/api/status':
        // 状态查询端点
        if ($method === 'GET') {
            $status = getStatus();
            echo json_encode($status);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;
        
    case '/api/clear':
        // 清空状态端点
        if ($method === 'POST') {
            $status = updateStatus([
                'action' => 'none',
                'execute' => false,
                'last_trigger_time' => null,
                'last_check_time' => null,
                'last_confirm_time' => null,
                'trigger_count' => 0,
                'data' => null,
                'confirm_data' => null
            ]);
            logMessage("状态已清空");
            echo json_encode(['success' => true, 'message' => '状态已清空', 'status' => $status]);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;
        
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Not found', 'path' => $requestUri]);
        break;
}
?>
