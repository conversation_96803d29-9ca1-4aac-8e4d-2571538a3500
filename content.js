// 内容脚本 - 在页面中注入监听功能
console.log('WeChat Image Monitor 内容脚本已加载');

// 初始化监听器
function initializeListeners() {
  try {
    // 避免重复初始化
    if (initializeListeners._initialized) {
      console.log('📊 监听器已初始化，跳过');
      return;
    }
    
    initializeListeners._initialized = true;
    console.log('📊 开始初始化监听器...');
    
    // 设置网络响应监听
    setupNetworkResponseListener();

    // 设置 msg-tips 元素监听器
    setupMsgTipsElementListener();

    // 监听页面中的图片加载
    const observer = new MutationObserver(function(mutations) {
      try {
        mutations.forEach(function(mutation) {
          mutation.addedNodes.forEach(function(node) {
            try {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // 检查新添加的元素中的图片
                const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                images.forEach(checkImage);
                
                // 如果新添加的元素本身就是图片
                if (node.tagName === 'IMG') {
                  checkImage(node);
                }
              }
            } catch (error) {
              console.log('❌ 处理DOM节点错误:', error);
            }
          });
        });
      } catch (error) {
        console.log('❌ MutationObserver错误:', error);
      }
    });
    
    // 开始观察
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    // 检查页面中现有的图片
    const existingImages = document.querySelectorAll('img');
    existingImages.forEach(checkImage);
    
    console.log('📊 监听器初始化完成');
  } catch (error) {
    console.log('❌ 初始化监听器错误:', error);
  }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeListeners);
} else {
  initializeListeners();
}

// 检查图片是否来自目标URL
function checkImage(img) {
  try {
    if (img.src && (img.src.includes('mp.weixin.qq.com/cgi-bin/getimgdata') || img.src.includes('mp.weixin.qq.com/cgi-bin/singlesendpage'))) {
      const urlType = img.src.includes('getimgdata') ? '图片数据包' : '单发页面';
      console.log(`🔍 页面中检测到微信公众号${urlType}:`, img.src);
      console.log('📅 检测时间:', new Date().toLocaleString('zh-CN'));
      
      // 检查扩展上下文是否有效
      if (chrome && chrome.runtime && chrome.runtime.id) {
        // 发送消息到background脚本
        chrome.runtime.sendMessage({
          type: 'PAGE_TARGET_DETECTED',
          url: img.src,
          urlType: urlType,
          timestamp: new Date().toISOString()
        }).catch(error => {
          console.log('❌ 发送消息失败:', error);
        });
      } else {
        console.log('⚠️ 扩展上下文无效，跳过消息发送');
      }
    }
  } catch (error) {
    console.log('❌ checkImage函数错误:', error);
  }
}

// 监听网络请求（通过fetch拦截）- 完全重写，专注于getnewmsgnum
const originalFetch = window.fetch;
window.fetch = function(...args) {
  // 获取请求参数
  const url = args[0];
  let method = 'GET';
  let body = null;
  
  // 如果第二个参数存在，提取method和body
  if (args.length > 1 && args[1] && typeof args[1] === 'object') {
    method = args[1].method || 'GET';
    body = args[1].body || null;
  }
  
  // 检查是否是getnewmsgnum请求
  const isGetNewMsgNum = typeof url === 'string' && url.includes('getnewmsgnum');
  
  if (isGetNewMsgNum) {
    console.log('🔍🔍🔍 检测到getnewmsgnum fetch请求 🔍🔍🔍');
    console.log('🔍 请求URL:', url);
    console.log('🔍 请求方法:', method);
    console.log('🔍 请求体:', body);
    console.log('🔍 检测时间:', new Date().toLocaleString('zh-CN'));
    
    // 执行原始fetch请求并监听响应
    return originalFetch.apply(this, args).then(response => {
      console.log('📊 getnewmsgnum fetch响应状态:', response.status);
      
      // 克隆响应以便读取内容
      const clonedResponse = response.clone();
      
      // 处理响应
      clonedResponse.text().then(text => {
        console.log('📊📊📊 getnewmsgnum fetch响应已接收 📊📊📊');
        console.log('📊 请求URL:', url);
        console.log('📊 请求方法:', method);
        console.log('📊 响应状态:', response.status);
        console.log('📊 响应文本:', text);
        
        try {
          const data = JSON.parse(text);
          console.log('📊 解析后的响应数据:', data);
          console.log('📊 newTotalMsgCount值:', data.newTotalMsgCount);
          
          if (data.newTotalMsgCount > 0) {
            console.log('🔔 检测到新消息数量:', data.newTotalMsgCount);
            clickNewMessageElement();
          } else {
            console.log('📭 没有新消息 (newTotalMsgCount =', data.newTotalMsgCount, ')');
          }
          
          // 发送消息到background.js
          try {
            if (chrome && chrome.runtime && chrome.runtime.id) {
              chrome.runtime.sendMessage({
                type: 'GETNEWMSGNUM_RESPONSE',
                url: url,
                method: method,
                status: response.status,
                responseText: text,
                data: data,
                newTotalMsgCount: data.newTotalMsgCount,
                timestamp: new Date().toISOString()
              }).catch(err => console.log('发送消息失败:', err));
            }
          } catch (err) {
            console.log('发送消息错误:', err);
          }
        } catch (e) {
          console.log('❌ 解析getnewmsgnum响应失败:', e);
          console.log('❌ 原始响应文本:', text);
        }
      }).catch(error => {
        console.log('❌ 读取getnewmsgnum响应失败:', error);
      });
      
      // 返回原始响应
      return response;
    }).catch(error => {
      console.log('❌ getnewmsgnum fetch请求失败:', error);
      // 继续抛出错误，保持原始行为
      throw error;
    });
  }
  
  // 其他类型的请求也记录，但不详细处理
  else if (typeof url === 'string' && (url.includes('getimgdata') || url.includes('singlesendpage'))) {
    let urlType = url.includes('getimgdata') ? '图片数据包' : '单发页面';
    console.log(`🔍 检测到${urlType} fetch请求:`, url);
    console.log('📅 检测时间:', new Date().toLocaleString('zh-CN'));
    
    // 检查扩展上下文是否有效
    if (chrome && chrome.runtime && chrome.runtime.id) {
      chrome.runtime.sendMessage({
        type: 'FETCH_TARGET_DETECTED',
        url: url,
        urlType: urlType,
        method: method,
        timestamp: new Date().toISOString()
      }).catch(error => {
        console.log('❌ 发送fetch消息失败:', error);
      });
    }
  }
  
  // 对于所有请求，执行原始fetch
  return originalFetch.apply(this, args);
};

// 监听XMLHttpRequest - 完全重写，专注于getnewmsgnum
const originalXHROpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function(method, url, ...args) {
  // 保存原始方法和URL
  const originalMethod = method;
  const originalUrl = url;
  
  // 保存URL到XHR对象，无论是什么URL
  this._url = url;
  this._method = method;
  
  // 检查是否是getnewmsgnum请求
  const isGetNewMsgNum = typeof url === 'string' && url.includes('getnewmsgnum');
  
  if (isGetNewMsgNum) {
    console.log('🔍🔍🔍 检测到getnewmsgnum请求 🔍🔍🔍');
    console.log('🔍 请求URL:', url);
    console.log('🔍 请求方法:', method);
    console.log('🔍 检测时间:', new Date().toLocaleString('zh-CN'));
    
    // 添加所有可能的事件监听器来捕获响应
    
    // 1. readystatechange事件 - 最可靠的方式
    this.addEventListener('readystatechange', function() {
      if (this.readyState === 4) {
        console.log('📊📊📊 getnewmsgnum响应已接收 📊📊📊');
        console.log('📊 请求URL:', this._url);
        console.log('📊 请求方法:', this._method);
        console.log('📊 响应状态:', this.status);
        console.log('📊 响应头:', this.getAllResponseHeaders());
        console.log('📊 响应文本:', this.responseText);
        
        try {
          const data = JSON.parse(this.responseText);
          console.log('📊 解析后的响应数据:', data);
          console.log('📊 newTotalMsgCount值:', data.newTotalMsgCount);
          
          if (data.newTotalMsgCount > 0) {
            console.log('🔔 检测到新消息数量:', data.newTotalMsgCount);
            clickNewMessageElement();
          } else {
            console.log('📭 没有新消息 (newTotalMsgCount =', data.newTotalMsgCount, ')');
          }
          
          // 发送消息到background.js
          try {
            if (chrome && chrome.runtime && chrome.runtime.id) {
              chrome.runtime.sendMessage({
                type: 'GETNEWMSGNUM_RESPONSE',
                url: this._url,
                method: this._method,
                status: this.status,
                responseText: this.responseText,
                data: data,
                newTotalMsgCount: data.newTotalMsgCount,
                timestamp: new Date().toISOString()
              }).catch(err => console.log('发送消息失败:', err));
            }
          } catch (err) {
            console.log('发送消息错误:', err);
          }
        } catch (e) {
          console.log('❌ 解析getnewmsgnum响应失败:', e);
          console.log('❌ 原始响应文本:', this.responseText);
        }
      }
    });
    
    // 2. load事件 - 请求完成时触发
    this.addEventListener('load', function() {
      console.log('📊 getnewmsgnum load事件触发');
      console.log('📊 响应文本 (load):', this.responseText);
    });
    
    // 3. 重写send方法来捕获请求体
    const originalSend = this.send;
    this.send = function(body) {
      console.log('🔍 getnewmsgnum请求发送中...');
      console.log('🔍 请求体:', body);
      
      // 调用原始send方法
      return originalSend.apply(this, arguments);
    };
  }
  
  // 其他类型的请求也记录，但不详细处理
  else if (typeof url === 'string' && (url.includes('getimgdata') || url.includes('singlesendpage'))) {
    let urlType = url.includes('getimgdata') ? '图片数据包' : '单发页面';
    console.log(`🔍 检测到${urlType}请求:`, url);
    console.log('📅 检测时间:', new Date().toLocaleString('zh-CN'));
    
    // 检查扩展上下文是否有效
    if (chrome && chrome.runtime && chrome.runtime.id) {
      chrome.runtime.sendMessage({
        type: 'XHR_TARGET_DETECTED',
        url: url,
        urlType: urlType,
        timestamp: new Date().toISOString()
      }).catch(error => {
        console.log('❌ 发送XHR消息失败:', error);
      });
    }
  }
  
  // 调用原始open方法
  return originalXHROpen.apply(this, [originalMethod, originalUrl, ...args]);
};

// 监听来自background的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  try {
    console.log('📣 收到消息:', request.type);
    
    if (request.type === 'MONITOR_GETNEWMSGNUM') {
      monitorGetNewMsgNumResponse();
      console.log('📊 收到监听getnewmsgnum响应的请求');
      console.log('📊 URL:', request.url);
      console.log('📊 时间戳:', request.timestamp);
    } else if (request.type === 'GETNEWMSGNUM_COMPLETED') {
      console.log('📊 收到getnewmsgnum完成通知:', request.url);
      console.log('📊 状态码:', request.statusCode);
      console.log('📊 响应大小:', request.responseSize);
      console.log('📊 时间戳:', request.timestamp);
      
      // 主动获取响应内容
      fetchGetNewMsgNumResponse(request.url);
    } else if (request.type === 'CLICK_NEW_MESSAGE') {
      console.log('🔔 收到点击新消息元素的请求');
      console.log('🔔 新消息数量:', request.count);
      console.log('🔔 时间戳:', request.timestamp);
      
      // 点击新消息元素
      clickNewMessageElement();
    } else if (request.type === 'TEST_GETNEWMSGNUM') {
      // 测试响应监听
      console.log('🧪 测试getnewmsgnum响应监听');
      testGetNewMsgNumResponse();
    }
  } catch (error) {
    console.log('❌ 消息监听器错误:', error);
  }
});

// 测试getnewmsgnum响应监听
function testGetNewMsgNumResponse() {
  console.log('🧪 测试getnewmsgnum响应监听');
  console.log('🧪 getnewmsgnum是POST请求，不发送模拟请求');
  console.log('🧪 已设置监听器，等待原始请求的响应...');
  console.log('🧪 请在微信公众平台页面上操作，触发原始的getnewmsgnum请求');
  
  // 显示监听状态
  console.log('🧪 当前监听状态:');
  console.log('🧪 - XMLHttpRequest拦截: 已启用');
  console.log('🧪 - onreadystatechange拦截: 已启用');
  console.log('🧪 - Performance API监听: 已启用');
  
  // 检查当前页面的URL
  console.log('🧪 当前页面URL:', window.location.href);
  console.log('🧪 如果当前不在微信公众平台页面，请导航到微信公众平台');
}

// 监听getnewmsgnum响应
function monitorGetNewMsgNumResponse() {
  console.log('📊 开始监听getnewmsgnum响应...');
  
  // 避免重复设置监听器
  if (monitorGetNewMsgNumResponse._initialized) {
    console.log('📊 getnewmsgnum监听器已初始化，跳过');
    return;
  }
  
  monitorGetNewMsgNumResponse._initialized = true;
  console.log('📊 getnewmsgnum监听器已初始化');
  
  // 不再重新定义fetch，避免与全局fetch拦截冲突
  console.log('📊 使用全局fetch拦截器监听getnewmsgnum响应');
}

// 检查getnewmsgnum响应
function checkGetNewMsgNumResponse(url) {
  console.log('📊 检查getnewmsgnum响应:', url);
  console.log('📊 此函数已废弃，不再模拟请求');
}

// 跟踪已处理的请求，避免重复处理
const processedContentRequests = new Set();

// 主动获取getnewmsgnum响应内容
function fetchGetNewMsgNumResponse(url) {
  // 检查是否已经处理过此请求
  const requestKey = url.split('?')[0] + Date.now().toString().substring(0, 8);
  if (processedContentRequests.has(requestKey)) {
    console.log('📊 已处理过类似请求，跳过:', url);
    return;
  }
  
  // 将请求标记为已处理
  processedContentRequests.add(requestKey);
  
  // 5秒后从集合中删除，允许将来处理类似请求
  setTimeout(() => {
    processedContentRequests.delete(requestKey);
  }, 5000);
  
  console.log('📊 主动获取getnewmsgnum响应内容:', url);
  console.log('📊 ===== getnewmsgnum主动获取响应内容 =====');
  console.log('📊 请求URL:', url);
  console.log('📊 分析时间:', new Date().toISOString());
  console.log('📊 ===== getnewmsgnum主动获取响应结束 =====');
  
  // 不再发起新的fetch请求
}

// 监听网络请求的响应
function setupNetworkResponseListener() {
  // 避免重复设置监听器
  if (setupNetworkResponseListener._initialized) {
    console.log('📊 网络响应监听器已初始化，跳过');
    return;
  }
  
  setupNetworkResponseListener._initialized = true;
  console.log('📊 网络响应监听器已设置');
  
  // 添加全局网络请求监听
  if (window.performance && window.performance.getEntriesByType) {
    // 定期检查网络请求
    setInterval(() => {
      try {
        const entries = window.performance.getEntriesByType('resource');
        entries.forEach(entry => {
          if (entry.name && entry.name.includes('getnewmsgnum') && !entry._processed) {
            console.log('📊 检测到getnewmsgnum网络请求 (Performance API):', entry.name);
            console.log('📊 请求方法:', entry.initiatorType);
            console.log('📊 请求时间:', entry.startTime);
            console.log('📊 响应时间:', entry.responseEnd);
            console.log('📊 传输大小:', entry.transferSize);
            entry._processed = true; // 标记为已处理
            
            // 记录请求信息
            fetchResponseContent(entry.name);
          }
        });
      } catch (e) {
        console.log('❌ Performance API检查失败:', e);
      }
    }, 3000); // 每3秒检查一次
  }
  
  // 监听所有网络请求
  const originalSend = XMLHttpRequest.prototype.send;
  XMLHttpRequest.prototype.send = function(...args) {
    if (this._url && this._url.includes('getnewmsgnum')) {
      console.log('📊 拦截到getnewmsgnum XHR send调用:', this._url);
      
      // 添加load事件监听
      this.addEventListener('load', function() {
        console.log('📊 ===== getnewmsgnum XHR load事件 =====');
        console.log('📊 请求URL:', this._url);
        console.log('📊 响应状态:', this.status);
        console.log('📊 响应文本:', this.responseText);
        console.log('📊 响应头:', this.getAllResponseHeaders());
        
        try {
          const data = JSON.parse(this.responseText);
          console.log('📊 解析后的响应数据:', data);
          console.log('📊 newTotalMsgCount值:', data.newTotalMsgCount);
          
          if (data.newTotalMsgCount > 0) {
            console.log('🔔 检测到新消息数量:', data.newTotalMsgCount);
            clickNewMessageElement();
          } else {
            console.log('📭 没有新消息 (newTotalMsgCount =', data.newTotalMsgCount, ')');
          }
        } catch (e) {
          console.log('❌ 解析getnewmsgnum load响应失败:', e);
          console.log('❌ 原始响应文本:', this.responseText);
        }
        console.log('📊 ===== getnewmsgnum XHR load事件结束 =====');
      });
    }
    return originalSend.apply(this, args);
  };
  
  // 添加网络请求拦截器
  setupNetworkInterceptor();
}

// 设置网络请求拦截器
function setupNetworkInterceptor() {
  console.log('📊 设置网络请求拦截器...');
  
  // 创建一个新的XHR对象
  const XHR = XMLHttpRequest.prototype;
  
  // 保存原始的open方法
  const originalOpen = XHR.open;
  
  // 覆盖open方法
  XHR.open = function() {
    const method = arguments[0];
    const url = arguments[1];
    
    // 保存原始的onreadystatechange处理函数
    const originalOnReadyStateChange = this.onreadystatechange;
    
    // 设置新的onreadystatechange处理函数
    this.onreadystatechange = function() {
      try {
        if (this.readyState === 4) {
          if (url && url.toString().includes('getnewmsgnum')) {
            console.log('📊 ===== 拦截到getnewmsgnum响应 (onreadystatechange) =====');
            console.log('📊 请求URL:', url);
            console.log('📊 响应状态:', this.status);
            console.log('📊 响应类型:', this.responseType);
            
            // 打印响应文本
            if (this.responseText) {
              console.log('📊 响应文本:', this.responseText);
              
              try {
                const data = JSON.parse(this.responseText);
                console.log('📊 解析后的响应数据:', data);
                console.log('📊 newTotalMsgCount值:', data.newTotalMsgCount);
                
                if (data.newTotalMsgCount > 0) {
                  console.log('🔔 检测到新消息数量:', data.newTotalMsgCount);
                }
              } catch (e) {
                console.log('❌ 解析响应失败:', e);
              }
            } else {
              console.log('❌ 响应文本为空');
            }
            
            console.log('📊 ===== getnewmsgnum响应结束 (onreadystatechange) =====');
          }
        }
      } catch (e) {
        console.log('❌ onreadystatechange错误:', e);
      }
      
      // 调用原始的onreadystatechange处理函数
      if (originalOnReadyStateChange) {
        originalOnReadyStateChange.apply(this, arguments);
      }
    };
    
    // 调用原始的open方法
    return originalOpen.apply(this, arguments);
  };
}

// 记录检测到的响应内容
function fetchResponseContent(url) {
  if (!url || typeof url !== 'string') {
    return;
  }
  
  console.log('📊 检测到getnewmsgnum请求:', url);
  console.log('📊 getnewmsgnum是POST请求，不发送模拟请求');
  console.log('📊 等待原始请求的响应...');
  
  // 记录请求信息
  const requestInfo = {
    url: url,
    timestamp: new Date().toISOString(),
    detected: true
  };
  
  // 将请求信息存储到本地存储中
  try {
    chrome.storage.local.get(['getnewmsgnumRequests'], function(result) {
      const requests = result.getnewmsgnumRequests || [];
      requests.unshift(requestInfo);
      
      // 限制存储数量
      if (requests.length > 10) {
        requests.splice(10);
      }
      
      chrome.storage.local.set({ 'getnewmsgnumRequests': requests });
    });
  } catch (e) {
    console.log('❌ 存储请求信息失败:', e);
  }
}

// 点击新消息元素
function clickNewMessageElement() {
  const xpath = '//*[@id="app"]/div[5]/div[1]/div[1]/div[1]/span[1]';

  try {
    const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
    const element = result.singleNodeValue;

    if (element) {
      console.log('🎯 找到新消息元素，准备点击');
      element.click();
      console.log('✅ 已点击新消息元素');

      // 检查扩展上下文是否有效
      if (chrome && chrome.runtime && chrome.runtime.id) {
        // 发送点击成功消息
        chrome.runtime.sendMessage({
          type: 'NEW_MESSAGE_CLICKED',
          timestamp: new Date().toISOString()
        }).catch(error => {
          console.log('❌ 发送点击消息失败:', error);
        });
      } else {
        console.log('⚠️ 扩展上下文无效，跳过点击消息发送');
      }
    } else {
      console.log('❌ 未找到新消息元素');
    }
  } catch (e) {
    console.log('❌ 点击新消息元素失败:', e);
  }
}

// 监听特定元素出现并执行点击序列
function setupMsgTipsElementListener() {
  console.log('🔍 开始监听 msg-tips 元素...');

  // 目标元素选择器
  const msgTipsSelector = '#app > div.msg-tips';
  const firstClickSelector = '#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(1)';
  const secondClickSelector = '#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(2)';

  // 创建观察器来监听DOM变化
  const msgTipsObserver = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      // 检查新增的节点
      mutation.addedNodes.forEach(function(node) {
        if (node.nodeType === Node.ELEMENT_NODE) {
          // 检查新增的节点是否是目标元素或包含目标元素
          let msgTipsElement = null;

          if (node.matches && node.matches(msgTipsSelector)) {
            msgTipsElement = node;
          } else if (node.querySelector) {
            msgTipsElement = node.querySelector(msgTipsSelector);
          }

          if (msgTipsElement) {
            console.log('🎯 检测到 msg-tips 元素出现！');
            console.log('🎯 元素:', msgTipsElement);
            console.log('🎯 时间:', new Date().toLocaleString('zh-CN'));

            // 执行点击序列
            executeMsgTipsClickSequence(firstClickSelector, secondClickSelector);
          }
        }
      });
    });
  });

  // 开始观察整个文档的变化
  msgTipsObserver.observe(document.body, {
    childList: true,
    subtree: true
  });

  // 检查页面中是否已经存在目标元素
  const existingMsgTips = document.querySelector(msgTipsSelector);
  if (existingMsgTips) {
    console.log('🎯 页面中已存在 msg-tips 元素！');
    console.log('🎯 元素:', existingMsgTips);
    console.log('🎯 时间:', new Date().toLocaleString('zh-CN'));

    // 执行点击序列
    executeMsgTipsClickSequence(firstClickSelector, secondClickSelector);
  }

  console.log('✅ msg-tips 元素监听器已设置');
}

// 执行点击序列：先点击第一个元素，等待10秒后点击第二个元素
function executeMsgTipsClickSequence(firstSelector, secondSelector) {
  console.log('🚀 开始执行点击序列...');

  try {
    // 点击第一个元素
    const firstElement = document.querySelector(firstSelector);
    if (firstElement) {
      console.log('🎯 找到第一个目标元素，准备点击');
      console.log('🎯 元素:', firstElement);
      firstElement.click();
      console.log('✅ 已点击第一个元素');

      // 发送消息到background
      if (chrome && chrome.runtime && chrome.runtime.id) {
        chrome.runtime.sendMessage({
          type: 'FIRST_ELEMENT_CLICKED',
          selector: firstSelector,
          timestamp: new Date().toISOString()
        }).catch(error => {
          console.log('❌ 发送第一次点击消息失败:', error);
        });
      }

      // 等待10秒后点击第二个元素
      console.log('⏰ 等待10秒后点击第二个元素...');
      setTimeout(() => {
        try {
          const secondElement = document.querySelector(secondSelector);
          if (secondElement) {
            console.log('🎯 找到第二个目标元素，准备点击');
            console.log('🎯 元素:', secondElement);
            secondElement.click();
            console.log('✅ 已点击第二个元素');

            // 发送消息到background
            if (chrome && chrome.runtime && chrome.runtime.id) {
              chrome.runtime.sendMessage({
                type: 'SECOND_ELEMENT_CLICKED',
                selector: secondSelector,
                timestamp: new Date().toISOString()
              }).catch(error => {
                console.log('❌ 发送第二次点击消息失败:', error);
              });
            }
          } else {
            console.log('❌ 10秒后未找到第二个目标元素');
            console.log('❌ 选择器:', secondSelector);
          }
        } catch (e) {
          console.log('❌ 点击第二个元素失败:', e);
        }
      }, 10000); // 10秒 = 10000毫秒

    } else {
      console.log('❌ 未找到第一个目标元素');
      console.log('❌ 选择器:', firstSelector);
    }
  } catch (e) {
    console.log('❌ 执行点击序列失败:', e);
  }
}