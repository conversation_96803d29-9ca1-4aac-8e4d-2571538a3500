// 内容脚本 - 在页面中注入监听功能
console.log('WeChat Image Monitor 内容脚本已加载');

// 初始化监听器
function initializeListeners() {
  try {
    // 避免重复初始化
    if (initializeListeners._initialized) {
      console.log('📊 监听器已初始化，跳过');
      return;
    }
    
    initializeListeners._initialized = true;
    console.log('📊 开始初始化监听器...');
    
    // 设置网络响应监听
    setupNetworkResponseListener();

    // 设置 msg-tips 元素监听器
    setupMsgTipsElementListener();

    // 监听页面中的图片加载
    const observer = new MutationObserver(function(mutations) {
      try {
        mutations.forEach(function(mutation) {
          mutation.addedNodes.forEach(function(node) {
            try {
              if (node.nodeType === Node.ELEMENT_NODE) {
                // 检查新添加的元素中的图片
                const images = node.querySelectorAll ? node.querySelectorAll('img') : [];
                images.forEach(checkImage);
                
                // 如果新添加的元素本身就是图片
                if (node.tagName === 'IMG') {
                  checkImage(node);
                }
              }
            } catch (error) {
              console.log('❌ 处理DOM节点错误:', error);
            }
          });
        });
      } catch (error) {
        console.log('❌ MutationObserver错误:', error);
      }
    });
    
    // 开始观察
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    // 检查页面中现有的图片
    const existingImages = document.querySelectorAll('img');
    existingImages.forEach(checkImage);
    
    console.log('📊 监听器初始化完成');
  } catch (error) {
    console.log('❌ 初始化监听器错误:', error);
  }
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeListeners);
} else {
  initializeListeners();
}

// 检查图片是否来自目标URL
function checkImage(img) {
  try {
    if (img.src && (img.src.includes('mp.weixin.qq.com/cgi-bin/getimgdata') || img.src.includes('mp.weixin.qq.com/cgi-bin/singlesendpage'))) {
      const urlType = img.src.includes('getimgdata') ? '图片数据包' : '单发页面';
      console.log(`🔍 页面中检测到微信公众号${urlType}:`, img.src);
      console.log('📅 检测时间:', new Date().toLocaleString('zh-CN'));
      
      // 检查扩展上下文是否有效
      if (chrome && chrome.runtime && chrome.runtime.id) {
        // 发送消息到background脚本
        chrome.runtime.sendMessage({
          type: 'PAGE_TARGET_DETECTED',
          url: img.src,
          urlType: urlType,
          timestamp: new Date().toISOString()
        }).catch(error => {
          console.log('❌ 发送消息失败:', error);
        });
      } else {
        console.log('⚠️ 扩展上下文无效，跳过消息发送');
      }
    }
  } catch (error) {
    console.log('❌ checkImage函数错误:', error);
  }
}

// 监听网络请求（通过fetch拦截）
const originalFetch = window.fetch;
window.fetch = function(...args) {
  // 获取请求参数
  const url = args[0];
  let method = 'GET';

  // 如果第二个参数存在，提取method
  if (args.length > 1 && args[1] && typeof args[1] === 'object') {
    method = args[1].method || 'GET';
  }

  // 检查是否是目标请求
  if (typeof url === 'string' && (url.includes('getimgdata') || url.includes('singlesendpage'))) {
    let urlType = url.includes('getimgdata') ? '图片数据包' : '单发页面';
    console.log(`🔍 检测到${urlType} fetch请求:`, url);
    console.log('📅 检测时间:', new Date().toLocaleString('zh-CN'));

    // 检查扩展上下文是否有效
    if (chrome && chrome.runtime && chrome.runtime.id) {
      chrome.runtime.sendMessage({
        type: 'FETCH_TARGET_DETECTED',
        url: url,
        urlType: urlType,
        method: method,
        timestamp: new Date().toISOString()
      }).catch(error => {
        console.log('❌ 发送fetch消息失败:', error);
      });
    }
  }

  // 对于所有请求，执行原始fetch
  return originalFetch.apply(this, args);
};

// 监听XMLHttpRequest
const originalXHROpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function(method, url, ...args) {
  // 保存原始方法和URL
  const originalMethod = method;
  const originalUrl = url;

  // 保存URL到XHR对象
  this._url = url;
  this._method = method;

  // 检查是否是目标请求
  if (typeof url === 'string' && (url.includes('getimgdata') || url.includes('singlesendpage'))) {
    let urlType = url.includes('getimgdata') ? '图片数据包' : '单发页面';
    console.log(`🔍 检测到${urlType}请求:`, url);
    console.log('📅 检测时间:', new Date().toLocaleString('zh-CN'));

    // 检查扩展上下文是否有效
    if (chrome && chrome.runtime && chrome.runtime.id) {
      chrome.runtime.sendMessage({
        type: 'XHR_TARGET_DETECTED',
        url: url,
        urlType: urlType,
        timestamp: new Date().toISOString()
      }).catch(error => {
        console.log('❌ 发送XHR消息失败:', error);
      });
    }
  }

  // 调用原始open方法
  return originalXHROpen.apply(this, [originalMethod, originalUrl, ...args]);
};

// 监听来自background的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  try {
    console.log('📣 收到消息:', request.type);

    if (request.type === 'EXECUTE_CLICK_SEQUENCE') {
      console.log('🎯 收到PHP触发的点击序列执行请求');
      console.log('📡 请求数据:', request.data);
      console.log('⏰ 请求时间:', request.timestamp);

      // 执行点击序列
      const firstClickSelector = '#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(1)';
      const secondClickSelector = '#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(2)';

      executeMsgTipsClickSequence(firstClickSelector, secondClickSelector);

      // 发送执行确认
      sendResponse({ success: true, executed: true });
    }

  } catch (error) {
    console.log('❌ 消息监听器错误:', error);
    sendResponse({ success: false, error: error.message });
  }
});



// 监听网络请求的响应
function setupNetworkResponseListener() {
  // 避免重复设置监听器
  if (setupNetworkResponseListener._initialized) {
    console.log('📊 网络响应监听器已初始化，跳过');
    return;
  }
  
  setupNetworkResponseListener._initialized = true;
  console.log('📊 网络响应监听器已设置');
  

  

  
  // 添加网络请求拦截器
  setupNetworkInterceptor();
}

// 设置网络请求拦截器
function setupNetworkInterceptor() {
  console.log('📊 设置网络请求拦截器...');
  
  // 创建一个新的XHR对象
  const XHR = XMLHttpRequest.prototype;
  
  // 保存原始的open方法
  const originalOpen = XHR.open;
  
  // 覆盖open方法
  XHR.open = function() {
    const method = arguments[0];
    const url = arguments[1];
    
    // 保存原始的onreadystatechange处理函数
    const originalOnReadyStateChange = this.onreadystatechange;
    
    // 设置新的onreadystatechange处理函数
    this.onreadystatechange = function() {
      try {
        if (this.readyState === 4) {
          // 可以在这里添加其他需要监听的请求处理
        }
      } catch (e) {
        console.log('❌ onreadystatechange错误:', e);
      }

      // 调用原始的onreadystatechange处理函数
      if (originalOnReadyStateChange) {
        originalOnReadyStateChange.apply(this, arguments);
      }
    };
    
    // 调用原始的open方法
    return originalOpen.apply(this, arguments);
  };
}





// 监听页面标题包含"私"字并执行点击序列 - 无限循环版本
function setupMsgTipsElementListener() {
  console.log('🔍 开始设置页面标题"私"字无限循环监听...');

  // 目标关键字和选择器
  const targetKeyword = '私';
  const firstClickSelector = '#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(1)';
  const secondClickSelector = '#app > div.recent_main_con.weui-desktop-layout__main__bd.weui-desktop-panel > div.left_con.left > div.select_tap > div.list_type > span:nth-child(2)';

  // 记录检测次数和上次标题
  let detectionCount = 0;
  let lastTitle = document.title;

  // 检查标题是否包含"私"字的函数
  function checkTitleForKeyword() {
    const currentTitle = document.title;
    if (currentTitle !== lastTitle) {
      console.log(`📝 页面标题变化: "${lastTitle}" -> "${currentTitle}"`);
      lastTitle = currentTitle;

      if (currentTitle.includes(targetKeyword)) {
        detectionCount++;
        console.log(`🎯 第${detectionCount}次检测到页面标题包含"${targetKeyword}"字！`);
        console.log('🎯 当前标题:', currentTitle);
        console.log('🎯 时间:', new Date().toLocaleString('zh-CN'));
        console.log('🔄 无限循环监听正常工作');

        // 执行点击序列
        executeMsgTipsClickSequence(firstClickSelector, secondClickSelector);
        return true;
      }
    }
    return false;
  }

  // 创建观察器来监听DOM变化（主要监听title元素的变化）
  const titleObserver = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      // 检查是否有title元素的变化
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(function(node) {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // 检查是否是title元素或包含title元素
            if (node.tagName === 'TITLE' || node.querySelector('title')) {
              checkTitleForKeyword();
            }
          }
        });
      }
      // 检查文本内容变化（title内容变化）
      else if (mutation.type === 'characterData') {
        if (mutation.target.parentNode && mutation.target.parentNode.tagName === 'TITLE') {
          checkTitleForKeyword();
        }
      }
    });
  });

  // 开始观察整个文档的变化 - 持续监听
  titleObserver.observe(document.head, {
    childList: true,
    subtree: true,
    characterData: true,
    characterDataOldValue: true
  });

  // 定时检查机制 - 确保无限循环监听页面标题
  const intervalCheck = setInterval(() => {
    try {
      checkTitleForKeyword();
    } catch (error) {
      console.log('❌ 定时检查标题错误:', error);
    }
  }, 2000); // 每2秒检查一次

  // 检查页面加载时的标题是否包含"私"字
  const initialTitle = document.title;
  console.log('📝 页面加载时的标题:', initialTitle);
  if (initialTitle.includes(targetKeyword)) {
    detectionCount++;
    console.log(`🎯 页面加载时发现第${detectionCount}次标题包含"${targetKeyword}"字！`);
    console.log('🎯 当前标题:', initialTitle);
    console.log('🎯 时间:', new Date().toLocaleString('zh-CN'));

    // 执行点击序列
    executeMsgTipsClickSequence(firstClickSelector, secondClickSelector);
  }

  // 监听器状态日志
  console.log('✅ 页面标题"私"字无限循环监听器已设置');
  console.log('🔄 MutationObserver: 持续监听页面标题变化');
  console.log('🕐 定时检查: 每2秒检查一次标题');
  console.log('♾️  无限循环: 标题包含"私"字时触发点击');
  console.log('🎯 监听关键字:', targetKeyword);

  // 定期输出监听状态
  setInterval(() => {
    console.log(`📊 监听状态报告 - 已检测${detectionCount}次包含"${targetKeyword}"字的标题 - 当前标题: "${document.title}" - ${new Date().toLocaleString('zh-CN')}`);
  }, 30000); // 每30秒输出一次状态
}

// 执行点击序列：先点击第一个元素，等待10秒后点击第二个元素 - 无限循环版本
function executeMsgTipsClickSequence(firstSelector, secondSelector) {
  const executionId = Date.now(); // 为每次执行生成唯一ID
  console.log(`🚀 开始执行第${executionId}次点击序列...`);
  console.log(`🔄 无限循环模式：每次页面标题包含"私"字都会执行此序列`);
  console.log(`📝 触发标题: "${document.title}"`);

  try {
    // 点击第一个元素
    const firstElement = document.querySelector(firstSelector);
    if (firstElement) {
      console.log(`🎯 [${executionId}] 找到第一个目标元素，准备点击`);
      console.log(`🎯 [${executionId}] 元素:`, firstElement);
      firstElement.click();
      console.log(`✅ [${executionId}] 已点击第一个元素`);

      // 发送消息到background
      if (chrome && chrome.runtime && chrome.runtime.id) {
        chrome.runtime.sendMessage({
          type: 'FIRST_ELEMENT_CLICKED',
          selector: firstSelector,
          executionId: executionId,
          timestamp: new Date().toISOString()
        }).catch(error => {
          console.log(`❌ [${executionId}] 发送第一次点击消息失败:`, error);
        });
      }

      // 等待10秒后点击第二个元素
      console.log(`⏰ [${executionId}] 等待10秒后点击第二个元素...`);
      console.log(`🔄 [${executionId}] 10秒后将完成此次循环，准备等待下次msg-tips出现`);

      setTimeout(() => {
        try {
          const secondElement = document.querySelector(secondSelector);
          if (secondElement) {
            console.log(`🎯 [${executionId}] 找到第二个目标元素，准备点击`);
            console.log(`🎯 [${executionId}] 元素:`, secondElement);
            secondElement.click();
            console.log(`✅ [${executionId}] 已点击第二个元素`);
            console.log(`🎉 [${executionId}] 点击序列完成！等待下次标题包含"私"字...`);

            // 发送消息到background
            if (chrome && chrome.runtime && chrome.runtime.id) {
              chrome.runtime.sendMessage({
                type: 'SECOND_ELEMENT_CLICKED',
                selector: secondSelector,
                executionId: executionId,
                timestamp: new Date().toISOString()
              }).catch(error => {
                console.log(`❌ [${executionId}] 发送第二次点击消息失败:`, error);
              });
            }
          } else {
            console.log(`❌ [${executionId}] 10秒后未找到第二个目标元素`);
            console.log(`❌ [${executionId}] 选择器:`, secondSelector);
            console.log(`🔄 [${executionId}] 序列未完成，但监听器继续等待下次标题包含"私"字`);
          }
        } catch (e) {
          console.log(`❌ [${executionId}] 点击第二个元素失败:`, e);
          console.log(`🔄 [${executionId}] 出现错误，但监听器继续工作`);
        }
      }, 10000); // 10秒 = 10000毫秒

    } else {
      console.log(`❌ [${executionId}] 未找到第一个目标元素`);
      console.log(`❌ [${executionId}] 选择器:`, firstSelector);
      console.log(`🔄 [${executionId}] 序列未执行，但监听器继续等待下次标题包含"私"字`);
    }
  } catch (e) {
    console.log(`❌ [${executionId}] 执行点击序列失败:`, e);
    console.log(`🔄 [${executionId}] 出现错误，但无限循环监听器继续工作`);
  }
}