# API请求故障排除指南

## 🚨 常见错误及解决方案

### 1. "Failed to fetch" 错误

**错误信息：**
```
❌ API请求失败: Error: Failed to fetch
```

**可能原因和解决方案：**

#### A. 本地API服务器未启动
**检查方法：**
- 在浏览器中访问 `http://127.0.0.1/api/send?openid=test`
- 如果无法访问，说明服务器未启动

**解决方案：**
1. 启动本地API服务器
2. 使用提供的测试服务器：`node test-api-server.js`

#### B. 端口权限问题
**错误信息：**
```
Error: EACCES: permission denied, bind 127.0.0.1:80
```

**解决方案：**
1. 使用管理员权限运行服务器
2. 或者使用其他端口（如8080）并修改扩展配置

#### C. CORS跨域问题
**解决方案：**
1. 确保API服务器设置了正确的CORS头部：
```javascript
res.setHeader('Access-Control-Allow-Origin', '*');
res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept');
```

2. 确保manifest.json中包含了host_permissions：
```json
"host_permissions": [
  "http://127.0.0.1/*",
  "http://localhost/*"
]
```

#### D. 防火墙阻止
**解决方案：**
1. 检查Windows防火墙设置
2. 允许Node.js或相关程序通过防火墙
3. 临时关闭防火墙测试

### 2. 网络连接超时

**错误信息：**
```
❌ API请求失败: Error: Network request failed
```

**解决方案：**
1. 检查本地网络连接
2. 确认127.0.0.1地址可访问
3. 尝试使用localhost替代127.0.0.1

### 3. API服务器响应错误

**错误信息：**
```
❌ API请求失败: 500 Internal Server Error
```

**解决方案：**
1. 检查API服务器日志
2. 确认API端点正确实现
3. 验证openid参数处理逻辑

## 🔧 调试步骤

### 1. 检查扩展权限
1. 打开Chrome扩展管理页面
2. 确认扩展已启用
3. 检查权限设置

### 2. 查看控制台日志
1. 打开Chrome开发者工具
2. 查看Console标签页
3. 寻找API请求相关的日志

### 3. 测试API服务器
1. 在浏览器中直接访问API地址
2. 使用curl命令测试：
```bash
curl "http://127.0.0.1/api/send?openid=test"
```

### 4. 检查网络请求
1. 打开Chrome开发者工具
2. 切换到Network标签页
3. 查看是否有API请求发出
4. 检查请求状态和响应

## 🛠️ 配置修改

### 修改API地址
如果需要使用不同的端口或地址，修改background.js：

```javascript
// 原始配置
const apiUrl = `http://127.0.0.1/api/send?openid=${tofakeid}`;

// 修改为其他端口
const apiUrl = `http://127.0.0.1:8080/api/send?openid=${tofakeid}`;

// 修改为其他地址
const apiUrl = `http://localhost:3000/api/send?openid=${tofakeid}`;
```

### 添加新的host_permissions
如果使用不同的地址，需要在manifest.json中添加权限：

```json
"host_permissions": [
  "https://mp.weixin.qq.com/*",
  "http://127.0.0.1/*",
  "http://localhost/*",
  "http://127.0.0.1:8080/*",
  "http://localhost:3000/*"
]
```

## 📋 测试清单

### 基础测试
- [ ] 本地API服务器已启动
- [ ] 浏览器可以访问API地址
- [ ] Chrome扩展已重新加载
- [ ] 扩展权限配置正确

### 功能测试
- [ ] 图片下载功能正常
- [ ] API请求在下载后触发
- [ ] popup中显示API请求记录
- [ ] 控制台显示详细日志

### 错误处理测试
- [ ] API服务器关闭时显示错误
- [ ] 网络异常时正确处理
- [ ] 错误信息正确记录和显示

## 🔍 常用调试命令

### 启动测试服务器
```bash
node test-api-server.js
```

### 测试API连接
```bash
curl -v "http://127.0.0.1/api/send?openid=test"
```

### 检查端口占用
```bash
netstat -an | findstr :80
```

### 查看Chrome扩展日志
1. 打开chrome://extensions/
2. 点击扩展的"详细信息"
3. 点击"检查视图"中的"背景页"

## 📞 获取帮助

如果问题仍然存在：
1. 检查所有日志输出
2. 确认API服务器配置
3. 验证网络连接
4. 重新加载Chrome扩展
5. 重启浏览器
