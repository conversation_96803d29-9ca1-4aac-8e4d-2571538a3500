<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>元素监听测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 5px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .msg-tips {
            background-color: #ffeb3b;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            display: none;
        }
        
        .recent_main_con {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .left_con.left .select_tap .list_type {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        
        .left_con.left .select_tap .list_type span {
            padding: 8px 16px;
            background-color: #2196f3;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .left_con.left .select_tap .list_type span:hover {
            background-color: #1976d2;
        }
        
        .left_con.left .select_tap .list_type span:nth-child(1) {
            background-color: #4caf50;
        }
        
        .left_con.left .select_tap .list_type span:nth-child(2) {
            background-color: #ff9800;
        }
        
        .test-button {
            background-color: #e91e63;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        .test-button:hover {
            background-color: #c2185b;
        }
        
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 元素监听功能测试页面</h1>
        <p>这个页面用于测试Chrome扩展的元素监听功能。</p>
        
        <div class="test-section">
            <h3>📋 测试说明</h3>
            <p>1. 确保Chrome扩展已安装并启用</p>
            <p>2. 打开浏览器开发者工具的Console标签页查看日志</p>
            <p>3. 点击下面的"显示msg-tips元素"按钮来触发监听</p>
            <p>4. 观察扩展是否自动点击了两个目标元素</p>
        </div>
        
        <div class="test-section">
            <h3>🎯 目标元素</h3>
            
            <!-- msg-tips 元素 (初始隐藏) -->
            <div id="app">
                <div class="msg-tips" id="msgTips">
                    ⚠️ 这是 msg-tips 元素 - 当它出现时会触发监听
                </div>
                
                <!-- 模拟微信公众平台的结构 -->
                <div class="recent_main_con weui-desktop-layout__main__bd weui-desktop-panel">
                    <div class="left_con left">
                        <div class="select_tap">
                            <div class="list_type">
                                <span id="firstTarget">第一个目标元素</span>
                                <span id="secondTarget">第二个目标元素</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 测试控制</h3>
            <button class="test-button" onclick="showMsgTips()">显示 msg-tips 元素</button>
            <button class="test-button" onclick="hideMsgTips()">隐藏 msg-tips 元素</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-section">
            <h3>📊 状态信息</h3>
            <div id="status" class="status info">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>📝 操作日志</h3>
            <div id="log" class="log">日志将在这里显示...\n</div>
        </div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        function showMsgTips() {
            const msgTips = document.getElementById('msgTips');
            msgTips.style.display = 'block';
            addLog('✅ msg-tips 元素已显示');
            updateStatus('msg-tips 元素已显示，等待扩展响应...', 'info');
        }
        
        function hideMsgTips() {
            const msgTips = document.getElementById('msgTips');
            msgTips.style.display = 'none';
            addLog('❌ msg-tips 元素已隐藏');
            updateStatus('msg-tips 元素已隐藏', 'info');
        }
        
        function clearLog() {
            logElement.textContent = '日志已清空...\n';
            updateStatus('日志已清空', 'info');
        }
        
        // 监听目标元素的点击事件
        document.getElementById('firstTarget').addEventListener('click', function() {
            addLog('🎯 第一个目标元素被点击了！');
            updateStatus('第一个目标元素被点击，等待10秒后点击第二个...', 'success');
        });
        
        document.getElementById('secondTarget').addEventListener('click', function() {
            addLog('🎯 第二个目标元素被点击了！');
            updateStatus('第二个目标元素被点击，测试完成！', 'success');
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('📄 测试页面已加载');
            updateStatus('测试页面已准备就绪', 'info');
        });
    </script>
</body>
</html>
