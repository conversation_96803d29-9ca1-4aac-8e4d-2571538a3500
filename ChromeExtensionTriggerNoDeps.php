<?php
/**
 * Chrome扩展WebSocket触发器 - 无依赖版本
 * 完全使用内置PHP功能，无需任何外部依赖
 */

require_once __DIR__ . '/SimpleWebSocketClient.php';

class ChromeExtensionTriggerNoDeps {
    private $serverUrl;
    private $projectName;
    private $timeout;
    
    public function __construct($serverUrl = 'ws://127.0.0.1:9998', $projectName = 'php_project', $timeout = 10) {
        $this->serverUrl = $serverUrl;
        $this->projectName = $projectName;
        $this->timeout = $timeout;
    }
    
    /**
     * 触发Chrome扩展执行点击操作
     * 
     * @param array $data 附加数据
     * @return array 结果
     */
    public function triggerClick($data = []) {
        $simpleClient = new ChromeExtensionTriggerSimple($this->serverUrl, $this->projectName, $this->timeout);
        return $simpleClient->triggerClick($data);
    }
    
    /**
     * 触发并等待执行完成
     * 
     * @param array $data 附加数据
     * @param int $waitTimeout 等待超时时间（秒）
     * @return array 结果
     */
    public function triggerAndWait($data = [], $waitTimeout = 30) {
        $simpleClient = new ChromeExtensionTriggerSimple($this->serverUrl, $this->projectName, $this->timeout);
        return $simpleClient->triggerAndWait($data, $waitTimeout);
    }
    
    /**
     * 测试WebSocket连接
     * 
     * @return bool 连接是否正常
     */
    public function testConnection() {
        $simpleClient = new ChromeExtensionTriggerSimple($this->serverUrl, $this->projectName, 5);
        return $simpleClient->testConnection();
    }
}

// ==================== 使用示例 ====================

if (php_sapi_name() === 'cli') {
    echo "🚀 Chrome扩展WebSocket触发器测试（无依赖版本）\n";
    echo "===============================================\n\n";
    
    $trigger = new ChromeExtensionTriggerNoDeps();
    
    // 测试连接
    echo "🔗 测试WebSocket连接...\n";
    if ($trigger->testConnection()) {
        echo "✅ WebSocket连接正常\n\n";
        
        // 示例1: 简单触发
        echo "📝 示例1: 简单触发\n";
        echo "-------------------\n";
        $result = $trigger->triggerClick([
            'action' => 'test_trigger',
            'user_id' => 123,
            'timestamp' => time()
        ]);
        
        if ($result['success']) {
            echo "✅ 触发成功！\n";
            echo "📊 消息ID: " . $result['message_id'] . "\n";
            echo "📤 发送到: " . $result['sent_to'] . " 个扩展\n";
        } else {
            echo "❌ 触发失败: " . $result['error'] . "\n";
        }
        
        echo "\n";
        
        // 示例2: 触发并等待确认
        echo "📝 示例2: 触发并等待确认\n";
        echo "------------------------\n";
        $result = $trigger->triggerAndWait([
            'action' => 'process_order',
            'order_id' => 'ORDER-2024-001',
            'priority' => 'high'
        ], 30);
        
        if ($result['success']) {
            echo "🎉 执行完成！\n";
            echo "📊 消息ID: " . $result['message_id'] . "\n";
            echo "🔌 扩展ID: " . $result['extension_id'] . "\n";
            echo "⏰ 完成时间: " . $result['timestamp'] . "\n";
        } else {
            echo "❌ 执行失败: " . $result['error'] . "\n";
        }
        
    } else {
        echo "❌ WebSocket连接失败\n";
        echo "💡 请确保:\n";
        echo "   1. WebSocket服务器已启动: php websocket-server.php\n";
        echo "   2. 端口9998未被占用\n";
        echo "   3. 防火墙允许连接\n";
    }
    
    echo "\n🎉 测试完成！\n";
    
} else {
    // Web模式
    header('Content-Type: text/html; charset=utf-8');
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Chrome扩展WebSocket触发器（无依赖版本）</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            button { padding: 12px 24px; margin: 8px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
            button:hover { background: #005a87; }
            .result { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; white-space: pre-wrap; font-family: monospace; font-size: 12px; }
            .success { border-left: 4px solid #28a745; }
            .error { border-left: 4px solid #dc3545; }
            .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #007cba; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Chrome扩展WebSocket触发器</h1>
            <p><strong>无依赖版本</strong> - 使用纯PHP实现，无需Composer依赖</p>
            
            <div class="info">
                <strong>📋 使用前检查:</strong><br>
                1. WebSocket服务器已启动: <code>php websocket-server.php</code><br>
                2. Chrome扩展已安装并重新加载<br>
                3. 目标网页已打开
            </div>
            
            <?php
            if (isset($_POST['action'])) {
                echo '<div class="result ';
                
                $trigger = new ChromeExtensionTriggerNoDeps();
                
                switch ($_POST['action']) {
                    case 'test':
                        echo 'info">🔗 测试WebSocket连接...' . "\n";
                        if ($trigger->testConnection()) {
                            echo "✅ WebSocket连接正常";
                        } else {
                            echo "❌ WebSocket连接失败\n请检查服务器是否启动";
                        }
                        break;
                        
                    case 'trigger':
                        echo 'info">🎯 触发Chrome扩展...' . "\n";
                        $result = $trigger->triggerClick([
                            'source' => 'web_interface',
                            'timestamp' => date('Y-m-d H:i:s'),
                            'test' => true
                        ]);
                        
                        if ($result['success']) {
                            echo "✅ 触发成功！\n";
                            echo "📊 消息ID: " . $result['message_id'] . "\n";
                            echo "📤 发送到: " . $result['sent_to'] . " 个扩展";
                        } else {
                            echo "❌ 触发失败: " . $result['error'];
                        }
                        break;
                        
                    case 'trigger_and_wait':
                        echo 'info">🎯 触发并等待执行完成...' . "\n";
                        $result = $trigger->triggerAndWait([
                            'source' => 'web_interface',
                            'action' => 'manual_trigger',
                            'timestamp' => date('Y-m-d H:i:s')
                        ], 30);
                        
                        if ($result['success']) {
                            echo "🎉 执行完成！\n";
                            echo "📊 消息ID: " . $result['message_id'] . "\n";
                            echo "🔌 扩展ID: " . $result['extension_id'] . "\n";
                            echo "⏰ 完成时间: " . $result['timestamp'];
                        } else {
                            echo "❌ 执行失败: " . $result['error'];
                        }
                        break;
                }
                
                echo '</div>';
            }
            ?>
            
            <h2>🎮 快速测试</h2>
            <form method="post">
                <button type="submit" name="action" value="test">🔗 测试连接</button>
                <button type="submit" name="action" value="trigger">🎯 触发点击</button>
                <button type="submit" name="action" value="trigger_and_wait">⏳ 触发并等待</button>
            </form>
            
            <h2>💻 PHP代码示例</h2>
            <div class="result">
&lt;?php
require_once 'ChromeExtensionTriggerNoDeps.php';

$trigger = new ChromeExtensionTriggerNoDeps();

// 简单触发
$result = $trigger->triggerClick([
    'user_id' => 123,
    'action' => 'process_user'
]);

if ($result['success']) {
    echo "触发成功！消息ID: " . $result['message_id'];
} else {
    echo "触发失败: " . $result['error'];
}

// 触发并等待完成
$result = $trigger->triggerAndWait([
    'order_id' => 'ORDER-001'
], 30);

if ($result['success']) {
    echo "执行完成！扩展ID: " . $result['extension_id'];
}
?&gt;
            </div>
            
            <h2>📚 文件说明</h2>
            <ul>
                <li><strong>ChromeExtensionTriggerNoDeps.php</strong> - 主要触发器类（无依赖）</li>
                <li><strong>SimpleWebSocketClient.php</strong> - 内置WebSocket客户端</li>
                <li><strong>websocket-server.php</strong> - WebSocket服务器（需要Ratchet）</li>
            </ul>
            
            <div class="info">
                <strong>💡 提示:</strong><br>
                此版本完全使用PHP内置功能，无需任何Composer依赖。<br>
                如果需要更高性能，可以安装外部WebSocket库。
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>
