<?php
/**
 * WebSocket服务器
 * 用于Chrome扩展和PHP项目之间的实时通信
 * 
 * 依赖: composer require ratchet/pawl
 * 启动: php websocket-server.php
 */

require_once __DIR__ . '/vendor/autoload.php';

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;

class ChromeExtensionWebSocket implements MessageComponentInterface {
    protected $clients;
    protected $chromeExtensions;
    protected $phpClients;
    
    public function __construct() {
        $this->clients = new \SplObjectStorage;
        $this->chromeExtensions = new \SplObjectStorage;
        $this->phpClients = new \SplObjectStorage;
        
        echo "🚀 WebSocket服务器启动中...\n";
    }
    
    public function onOpen(ConnectionInterface $conn) {
        $this->clients->attach($conn);
        echo "📱 新连接: {$conn->resourceId} (总连接数: " . count($this->clients) . ")\n";
    }
    
    public function onMessage(ConnectionInterface $from, $msg) {
        try {
            $data = json_decode($msg, true);
            if (!$data) {
                throw new Exception('无效的JSON消息');
            }
            
            $this->logMessage("收到消息", $from->resourceId, $data['type'] ?? 'unknown');
            
            switch ($data['type']) {
                case 'connection':
                    $this->handleConnection($from, $data);
                    break;
                    
                case 'trigger_click':
                    $this->handleTriggerClick($from, $data);
                    break;
                    
                case 'confirm_received':
                    $this->handleConfirmReceived($from, $data);
                    break;
                    
                case 'heartbeat':
                    $this->handleHeartbeat($from, $data);
                    break;
                    
                case 'ping':
                    $this->handlePing($from, $data);
                    break;
                    
                case 'pong':
                    $this->handlePong($from, $data);
                    break;
                    
                default:
                    $this->logMessage("未知消息类型", $from->resourceId, $data['type'] ?? 'undefined');
            }
        } catch (Exception $e) {
            $this->logMessage("处理消息错误", $from->resourceId, $e->getMessage());
        }
    }
    
    public function onClose(ConnectionInterface $conn) {
        $this->clients->detach($conn);
        $this->chromeExtensions->detach($conn);
        $this->phpClients->detach($conn);
        
        echo "🔌 连接关闭: {$conn->resourceId} (剩余连接数: " . count($this->clients) . ")\n";
    }
    
    public function onError(ConnectionInterface $conn, \Exception $e) {
        echo "❌ 连接错误 {$conn->resourceId}: {$e->getMessage()}\n";
        $conn->close();
    }
    
    private function handleConnection($conn, $data) {
        if (isset($data['client'])) {
            if ($data['client'] === 'chrome_extension') {
                $this->chromeExtensions->attach($conn);
                $conn->clientType = 'chrome_extension';
                $conn->extensionId = $data['extension_id'] ?? 'unknown';
                
                $this->logMessage("Chrome扩展已连接", $conn->resourceId, $conn->extensionId);
                
                // 发送连接确认
                $conn->send(json_encode([
                    'type' => 'connection_confirmed',
                    'server_time' => date('Y-m-d H:i:s'),
                    'client_id' => $conn->resourceId
                ]));
                
            } elseif ($data['client'] === 'php_project') {
                $this->phpClients->attach($conn);
                $conn->clientType = 'php_project';
                $conn->projectName = $data['project_name'] ?? 'unknown';
                
                $this->logMessage("PHP项目已连接", $conn->resourceId, $conn->projectName);
                
                // 发送连接确认
                $conn->send(json_encode([
                    'type' => 'connection_confirmed',
                    'server_time' => date('Y-m-d H:i:s'),
                    'client_id' => $conn->resourceId,
                    'chrome_extensions_count' => count($this->chromeExtensions)
                ]));
            }
        }
    }
    
    private function handleTriggerClick($from, $data) {
        $this->logMessage("触发点击请求", $from->resourceId, "发送到" . count($this->chromeExtensions) . "个扩展");
        
        // 生成消息ID
        $messageId = uniqid('trigger_', true);
        
        // 向所有Chrome扩展发送触发消息
        foreach ($this->chromeExtensions as $extension) {
            try {
                $message = [
                    'type' => 'trigger_click',
                    'id' => $messageId,
                    'data' => $data['data'] ?? [],
                    'source' => 'php_websocket',
                    'timestamp' => date('Y-m-d H:i:s'),
                    'from_client' => $from->resourceId
                ];
                
                $extension->send(json_encode($message));
                $this->logMessage("已发送触发消息", $extension->resourceId, $messageId);
            } catch (Exception $e) {
                $this->logMessage("发送失败", $extension->resourceId, $e->getMessage());
            }
        }
        
        // 向发送方确认
        $from->send(json_encode([
            'type' => 'trigger_sent',
            'message_id' => $messageId,
            'sent_to' => count($this->chromeExtensions),
            'timestamp' => date('Y-m-d H:i:s')
        ]));
    }
    
    private function handleConfirmReceived($from, $data) {
        $this->logMessage("收到执行确认", $from->resourceId, $data['message_id'] ?? 'unknown');
        
        // 向所有PHP客户端转发确认消息
        foreach ($this->phpClients as $phpClient) {
            try {
                $phpClient->send(json_encode([
                    'type' => 'execution_confirmed',
                    'message_id' => $data['message_id'] ?? null,
                    'extension_id' => $data['extension_id'] ?? null,
                    'timestamp' => date('Y-m-d H:i:s'),
                    'from_extension' => $from->resourceId
                ]));
            } catch (Exception $e) {
                $this->logMessage("转发确认失败", $phpClient->resourceId, $e->getMessage());
            }
        }
    }
    
    private function handleHeartbeat($from, $data) {
        // 响应心跳
        $from->send(json_encode([
            'type' => 'heartbeat_response',
            'server_time' => date('Y-m-d H:i:s'),
            'client_count' => count($this->clients)
        ]));
    }
    
    private function handlePing($from, $data) {
        $from->send(json_encode([
            'type' => 'pong',
            'timestamp' => date('Y-m-d H:i:s')
        ]));
    }
    
    private function handlePong($from, $data) {
        // 记录pong响应
        $this->logMessage("收到pong", $from->resourceId, "延迟检查");
    }
    
    private function logMessage($action, $clientId, $details) {
        $timestamp = date('Y-m-d H:i:s');
        echo "[$timestamp] 📡 $action - 客户端:$clientId - $details\n";
    }
    
    public function getStats() {
        return [
            'total_clients' => count($this->clients),
            'chrome_extensions' => count($this->chromeExtensions),
            'php_clients' => count($this->phpClients),
            'server_time' => date('Y-m-d H:i:s')
        ];
    }
}

// 创建WebSocket服务器
$server = IoServer::factory(
    new HttpServer(
        new WsServer(
            new ChromeExtensionWebSocket()
        )
    ),
    9998
);

echo "✅ WebSocket服务器已启动\n";
echo "🔗 监听地址: ws://127.0.0.1:9998\n";
echo "📡 等待Chrome扩展和PHP项目连接...\n";
echo "🛑 按 Ctrl+C 停止服务器\n\n";

$server->run();
?>
