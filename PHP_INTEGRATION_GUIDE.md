# PHP项目集成Chrome扩展指南

## 🎯 概述

本指南展示如何在您的PHP项目中集成Chrome扩展，通过HTTP API触发扩展执行点击操作。

## 🏗️ 架构说明

```
您的PHP项目 → HTTP API接口 ← Chrome扩展轮询
     ↓              ↓              ↓
  发送触发请求   →  设置执行标志  →  执行点击操作
     ↑              ↑              ↑
  接收执行确认   ←  清除执行标志  ←  发送完成确认
```

### 工作流程
1. **PHP项目**调用API接口设置触发标志
2. **Chrome扩展**轮询检查触发标志
3. **扩展**发现触发标志后执行点击操作
4. **扩展**完成后发送确认，清除触发标志

## 🚀 快速开始

### 1. 部署API接口
将 `php-project-example.php` 放在您的Web服务器上：

```bash
# 方法1: 使用PHP内置服务器（测试用）
php -S 127.0.0.1:9999 php-project-example.php

# 方法2: 放在现有Web服务器中
# 将文件放在 /var/www/html/chrome-api/ 目录下
# 访问地址: http://your-domain.com/chrome-api/php-project-example.php
```

### 2. 重新加载Chrome扩展
确保扩展已安装并重新加载以启用PHP监听功能。

### 3. 测试连接
访问API接口主页：`http://127.0.0.1:9999`

### 4. 在PHP项目中调用
```php
// 复制 ChromeExtensionTrigger 类到您的项目中
require_once 'chrome-extension-trigger.php';

$trigger = new ChromeExtensionTrigger('http://127.0.0.1:9999');
$result = $trigger->triggerClick(['user_id' => 123]);

if ($result['success']) {
    echo "Chrome扩展已触发！";
}
```

## 📡 API接口说明

### 核心端点

#### 1. `/api/trigger-chrome-click` (POST)
**用途**: 您的PHP代码调用此端点触发Chrome扩展

**请求示例**:
```php
$data = [
    'user_id' => 123,
    'action' => 'process_user',
    'priority' => 'high'
];

$response = file_get_contents('http://127.0.0.1:9999/api/trigger-chrome-click', false, 
    stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => json_encode($data)
        ]
    ])
);
```

**响应示例**:
```json
{
    "success": true,
    "message": "Chrome扩展触发指令已设置",
    "trigger_id": 5,
    "status": {
        "action": "trigger_click",
        "execute": true,
        "trigger_count": 5
    }
}
```

#### 2. `/chrome-extension/check` (GET)
**用途**: Chrome扩展轮询此端点检查触发指令

#### 3. `/chrome-extension/confirm` (POST)
**用途**: Chrome扩展发送执行确认

#### 4. `/api/status` (GET)
**用途**: 查看当前状态

## 💻 PHP集成示例

### 基本使用
```php
<?php
class ChromeExtensionTrigger {
    private $apiUrl;
    
    public function __construct($apiUrl = 'http://127.0.0.1:9999') {
        $this->apiUrl = rtrim($apiUrl, '/');
    }
    
    public function triggerClick($data = []) {
        $url = $this->apiUrl . '/api/trigger-chrome-click';
        
        $postData = array_merge([
            'timestamp' => date('Y-m-d H:i:s'),
            'source' => 'php_project'
        ], $data);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/json',
                'content' => json_encode($postData),
                'timeout' => 10
            ]
        ]);
        
        try {
            $response = file_get_contents($url, false, $context);
            return json_decode($response, true);
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}

// 使用示例
$trigger = new ChromeExtensionTrigger();
$result = $trigger->triggerClick(['user_id' => 123]);
?>
```

### 实际应用场景

#### 1. 用户注册后触发
```php
function onUserRegistered($userId, $userData) {
    $trigger = new ChromeExtensionTrigger();
    
    $result = $trigger->triggerClick([
        'event' => 'user_registered',
        'user_id' => $userId,
        'user_data' => $userData
    ]);
    
    if ($result['success']) {
        error_log("Chrome extension triggered for user: $userId");
    }
}
```

#### 2. 订单处理时触发
```php
function processOrder($orderId) {
    $trigger = new ChromeExtensionTrigger();
    
    $result = $trigger->triggerAndWait([
        'event' => 'order_processing',
        'order_id' => $orderId
    ], 60);
    
    return $result['success'];
}
```

#### 3. 定时任务中使用
```php
// cron任务
function dailyMaintenance() {
    $trigger = new ChromeExtensionTrigger();
    
    $result = $trigger->triggerClick([
        'event' => 'daily_maintenance',
        'scheduled_time' => date('Y-m-d H:i:s')
    ]);
    
    if ($result['success']) {
        echo "Daily maintenance triggered\n";
    }
}
```

#### 4. Web界面集成
```php
// 在管理后台添加触发按钮
if ($_POST['action'] == 'trigger_chrome') {
    $trigger = new ChromeExtensionTrigger();
    $result = $trigger->triggerClick([
        'source' => 'admin_panel',
        'admin_user' => $_SESSION['admin_id']
    ]);
    
    if ($result['success']) {
        $message = "Chrome扩展已触发！";
    } else {
        $message = "触发失败: " . $result['error'];
    }
}
```

## 🔧 配置选项

### 修改API地址
```php
// 如果您的API部署在其他地址
$trigger = new ChromeExtensionTrigger('http://your-domain.com:8080');
```

### 修改轮询频率
在 `background.js` 中修改：
```javascript
}, 5000); // 改为每5秒检查一次
```

### 自定义超时时间
```php
$result = $trigger->triggerAndWait($data, 120); // 等待120秒
```

## 🔍 调试和监控

### 查看日志
1. **Chrome扩展日志**: 打开开发者工具Console
2. **API服务器日志**: 查看Web服务器日志
3. **状态文件**: 查看 `chrome_extension_status.json`

### 状态监控
```php
$trigger = new ChromeExtensionTrigger();
$status = $trigger->getStatus();

echo "执行状态: " . ($status['execute'] ? '忙碌' : '空闲') . "\n";
echo "触发次数: " . $status['trigger_count'] . "\n";
```

## ⚠️ 注意事项

### 安全考虑
1. **访问控制**: 在生产环境中添加身份验证
2. **HTTPS**: 使用HTTPS保护数据传输
3. **输入验证**: 验证所有输入数据

### 性能优化
1. **轮询频率**: 根据需要调整检查频率
2. **超时设置**: 设置合理的超时时间
3. **错误处理**: 实现完善的错误处理机制

### 部署建议
1. **生产环境**: 使用专业Web服务器（Apache/Nginx）
2. **监控**: 添加健康检查和监控
3. **日志**: 实现详细的日志记录

## 🔄 故障排除

### 常见问题

#### 1. 扩展无法接收触发指令
- 检查扩展是否已重新加载
- 确认API地址和端口正确
- 查看扩展Console是否有错误

#### 2. API请求失败
- 检查API服务器是否运行
- 确认防火墙设置
- 验证请求格式是否正确

#### 3. 触发无响应
- 检查目标页面是否已打开
- 确认页面元素是否存在
- 查看扩展执行日志

### 调试步骤
1. 访问API主页检查服务状态
2. 查看Chrome扩展Console日志
3. 检查API服务器日志
4. 验证网络连接

现在您可以在任何PHP项目中集成Chrome扩展，实现远程触发点击操作！
