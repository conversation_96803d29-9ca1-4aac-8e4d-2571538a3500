#!/usr/bin/env python3
"""
Python WebSocket服务器
接收PHP的HTTP请求，通过WebSocket与Chrome扩展通信
"""

import asyncio
import websockets
import json
import uuid
import logging
from datetime import datetime
from aiohttp import web
import aiohttp_cors
from typing import Dict, Set

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ChromeExtensionWebSocketServer:
    def __init__(self):
        self.chrome_extensions: Set[websockets.WebSocketServerProtocol] = set()
        self.pending_requests: Dict[str, dict] = {}
        
    async def register_chrome_extension(self, websocket):
        """注册Chrome扩展连接"""
        self.chrome_extensions.add(websocket)
        logger.info(f"🔌 Chrome扩展已连接，总连接数: {len(self.chrome_extensions)}")
        
        # 发送注册成功消息
        await websocket.send(json.dumps({
            'type': 'register_success',
            'message': 'Chrome扩展注册成功',
            'timestamp': datetime.now().isoformat()
        }))
        
    async def unregister_chrome_extension(self, websocket):
        """注销Chrome扩展连接"""
        self.chrome_extensions.discard(websocket)
        logger.info(f"🔌 Chrome扩展已断开，剩余连接数: {len(self.chrome_extensions)}")
        
    async def handle_websocket(self, websocket, path):
        """处理WebSocket连接"""
        logger.info(f"📱 新的WebSocket连接: {websocket.remote_address}")
        
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    await self.process_websocket_message(websocket, data)
                except json.JSONDecodeError:
                    logger.error(f"❌ 无效的JSON消息: {message}")
                except Exception as e:
                    logger.error(f"❌ 处理WebSocket消息失败: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("🔌 WebSocket连接已关闭")
        except Exception as e:
            logger.error(f"❌ WebSocket连接错误: {e}")
        finally:
            await self.unregister_chrome_extension(websocket)
            
    async def process_websocket_message(self, websocket, data):
        """处理WebSocket消息"""
        message_type = data.get('type')
        logger.info(f"📡 收到WebSocket消息: {message_type}")
        
        if message_type == 'register':
            if data.get('client_type') == 'chrome_extension':
                await self.register_chrome_extension(websocket)
                
        elif message_type == 'execution_started':
            request_id = data.get('request_id')
            logger.info(f"🚀 Chrome扩展开始执行: {request_id}")
            
            # 更新请求状态
            if request_id in self.pending_requests:
                self.pending_requests[request_id]['status'] = 'executing'
                self.pending_requests[request_id]['started_at'] = datetime.now().isoformat()
                
        elif message_type == 'execution_complete':
            request_id = data.get('request_id')
            success = data.get('success', False)
            error = data.get('error')
            
            logger.info(f"✅ Chrome扩展执行完成: {request_id}, 成功: {success}")
            
            # 更新请求状态
            if request_id in self.pending_requests:
                self.pending_requests[request_id]['status'] = 'completed'
                self.pending_requests[request_id]['success'] = success
                self.pending_requests[request_id]['error'] = error
                self.pending_requests[request_id]['completed_at'] = datetime.now().isoformat()
                
        elif message_type == 'heartbeat':
            # 响应心跳
            await websocket.send(json.dumps({
                'type': 'heartbeat_response',
                'timestamp': datetime.now().isoformat()
            }))
            
        elif message_type == 'pong':
            logger.debug("🏓 收到pong响应")
            
    async def trigger_click_sequence(self, data):
        """触发Chrome扩展执行点击序列"""
        if not self.chrome_extensions:
            return {
                'success': False,
                'error': '没有可用的Chrome扩展连接'
            }
            
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 记录请求
        self.pending_requests[request_id] = {
            'request_id': request_id,
            'data': data,
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'started_at': None,
            'completed_at': None,
            'success': None,
            'error': None
        }
        
        # 构造消息
        message = {
            'type': 'execute_click_sequence',
            'request_id': request_id,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
        
        # 发送到所有Chrome扩展
        sent_count = 0
        for extension in self.chrome_extensions.copy():
            try:
                await extension.send(json.dumps(message))
                sent_count += 1
                logger.info(f"📤 已发送触发消息到Chrome扩展: {request_id}")
            except Exception as e:
                logger.error(f"❌ 发送消息到Chrome扩展失败: {e}")
                self.chrome_extensions.discard(extension)
                
        if sent_count > 0:
            return {
                'success': True,
                'request_id': request_id,
                'sent_to': sent_count,
                'message': f'触发消息已发送到 {sent_count} 个Chrome扩展'
            }
        else:
            return {
                'success': False,
                'error': '发送触发消息失败'
            }
            
    async def get_request_status(self, request_id):
        """获取请求状态"""
        return self.pending_requests.get(request_id, {
            'error': '请求ID不存在'
        })
        
    async def get_server_status(self):
        """获取服务器状态"""
        return {
            'chrome_extensions_count': len(self.chrome_extensions),
            'pending_requests_count': len([r for r in self.pending_requests.values() if r['status'] == 'pending']),
            'total_requests_count': len(self.pending_requests),
            'server_time': datetime.now().isoformat()
        }

# 全局服务器实例
server = ChromeExtensionWebSocketServer()

async def handle_trigger_request(request):
    """处理PHP的触发请求"""
    try:
        data = await request.json()
        logger.info(f"📥 收到PHP触发请求: {data}")
        
        result = await server.trigger_click_sequence(data)
        
        return web.json_response(result)
        
    except Exception as e:
        logger.error(f"❌ 处理触发请求失败: {e}")
        return web.json_response({
            'success': False,
            'error': str(e)
        }, status=500)

async def handle_status_request(request):
    """处理状态查询请求"""
    try:
        request_id = request.query.get('request_id')
        
        if request_id:
            # 查询特定请求状态
            status = await server.get_request_status(request_id)
            return web.json_response(status)
        else:
            # 查询服务器状态
            status = await server.get_server_status()
            return web.json_response(status)
            
    except Exception as e:
        logger.error(f"❌ 处理状态请求失败: {e}")
        return web.json_response({
            'success': False,
            'error': str(e)
        }, status=500)

async def handle_wait_request(request):
    """处理等待请求完成的请求"""
    try:
        data = await request.json()
        request_id = data.get('request_id')
        timeout = data.get('timeout', 30)
        
        if not request_id:
            return web.json_response({
                'success': False,
                'error': '缺少request_id参数'
            }, status=400)
            
        # 等待请求完成
        start_time = asyncio.get_event_loop().time()
        while asyncio.get_event_loop().time() - start_time < timeout:
            status = await server.get_request_status(request_id)
            
            if status.get('status') == 'completed':
                return web.json_response({
                    'success': True,
                    'completed': True,
                    'result': status
                })
                
            await asyncio.sleep(0.5)  # 每0.5秒检查一次
            
        # 超时
        return web.json_response({
            'success': False,
            'completed': False,
            'error': '等待超时',
            'timeout': timeout
        })
        
    except Exception as e:
        logger.error(f"❌ 处理等待请求失败: {e}")
        return web.json_response({
            'success': False,
            'error': str(e)
        }, status=500)

async def init_app():
    """初始化Web应用"""
    app = web.Application()
    
    # 配置CORS
    cors = aiohttp_cors.setup(app, defaults={
        "*": aiohttp_cors.ResourceOptions(
            allow_credentials=True,
            expose_headers="*",
            allow_headers="*",
            allow_methods="*"
        )
    })
    
    # 添加路由
    app.router.add_post('/trigger', handle_trigger_request)
    app.router.add_get('/status', handle_status_request)
    app.router.add_post('/wait', handle_wait_request)
    
    # 为所有路由添加CORS
    for route in list(app.router.routes()):
        cors.add(route)
        
    return app

async def main():
    """主函数"""
    logger.info("🚀 启动Python WebSocket服务器...")

    # 启动HTTP服务器
    app = await init_app()
    http_runner = web.AppRunner(app)
    await http_runner.setup()

    http_site = web.TCPSite(http_runner, '127.0.0.1', 8080)
    await http_site.start()

    # 启动WebSocket服务器
    websocket_server = await websockets.serve(
        server.handle_websocket,
        "127.0.0.1",
        8765
    )

    logger.info("✅ 服务器启动成功")
    logger.info("🔗 WebSocket地址: ws://127.0.0.1:8765")
    logger.info("🌐 HTTP API地址: http://127.0.0.1:8080")
    logger.info("📡 等待Chrome扩展连接...")
    logger.info("🛑 按 Ctrl+C 停止服务器")

    # 保持服务器运行
    try:
        await websocket_server.wait_closed()
    except KeyboardInterrupt:
        logger.info("🛑 收到停止信号")
    finally:
        # 清理资源
        websocket_server.close()
        await websocket_server.wait_closed()
        await http_runner.cleanup()
        logger.info("🛑 服务器已停止")
    
if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 服务器已停止")
