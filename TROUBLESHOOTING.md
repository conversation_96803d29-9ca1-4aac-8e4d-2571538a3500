# 故障排除指南

## 🔍 问题诊断

您遇到的问题是Python WebSocket服务器启动后立即退出。这通常有以下几个原因：

### 1. 事件循环问题
Python异步程序需要正确的事件循环管理。

### 2. 依赖问题
可能缺少必要的Python包。

### 3. 环境问题
Python版本或系统环境可能有问题。

## 🛠️ 解决步骤

### 步骤1: 检查Python环境
```bash
# 检查Python版本（需要3.7+）
python --version

# 检查是否安装了websockets
python -c "import websockets; print('websockets可用')"

# 检查是否安装了aiohttp
python -c "import aiohttp; print('aiohttp可用')"
```

### 步骤2: 安装依赖
```bash
# 安装基本依赖
pip install websockets

# 如果需要完整功能，安装所有依赖
pip install -r requirements.txt
```

### 步骤3: 测试基本功能
```bash
# 运行诊断程序
python start_server.py
```

### 步骤4: 运行简化服务器
```bash
# 运行最简单的测试服务器
python test_server.py
```

### 步骤5: 运行完整服务器
```bash
# 运行简化版本
python simple_websocket_server.py

# 或运行完整版本
python websocket_server.py
```

## 🔧 常见问题解决

### 问题1: 服务器立即退出
**原因**: 事件循环没有正确等待

**解决方案**:
1. 使用 `test_server.py` 测试基本功能
2. 检查是否有异常被抛出
3. 确保使用正确的Python版本

### 问题2: 模块导入错误
**错误信息**: `ModuleNotFoundError: No module named 'websockets'`

**解决方案**:
```bash
pip install websockets aiohttp aiohttp-cors
```

### 问题3: 端口被占用
**错误信息**: `OSError: [Errno 48] Address already in use`

**解决方案**:
```bash
# 检查端口占用
netstat -an | grep 8765
netstat -an | grep 8080

# 杀死占用进程
lsof -ti:8765 | xargs kill -9
lsof -ti:8080 | xargs kill -9
```

### 问题4: 权限问题
**错误信息**: `PermissionError`

**解决方案**:
```bash
# 使用不同端口
python test_server.py  # 使用8765端口

# 或以管理员权限运行
sudo python websocket_server.py
```

## 📝 推荐的测试顺序

### 1. 基础测试
```bash
python start_server.py
```
这会检查环境并运行基本的WebSocket服务器。

### 2. 功能测试
```bash
python test_server.py
```
这会启动一个简化的服务器，每10秒自动测试一次。

### 3. 完整测试
```bash
python simple_websocket_server.py
```
这会启动包含HTTP API的完整服务器。

## 🔍 日志分析

### 正常启动日志
```
2025-08-06 03:46:34,692 - INFO - 🚀 启动Python WebSocket服务器...
2025-08-06 03:46:34,709 - INFO - ✅ 服务器启动成功
2025-08-06 03:46:34,709 - INFO - 🔗 WebSocket地址: ws://127.0.0.1:8765
2025-08-06 03:46:34,709 - INFO - 🌐 HTTP API地址: http://127.0.0.1:8080
2025-08-06 03:46:34,709 - INFO - 📡 等待Chrome扩展连接...
2025-08-06 03:46:34,709 - INFO - 🛑 按 Ctrl+C 停止服务器
[服务器保持运行，不会退出]
```

### 异常退出日志
如果看到启动日志后立即退出，说明有异常发生。

## 🧪 手动测试

### 测试WebSocket连接
使用浏览器控制台测试：
```javascript
// 在浏览器控制台中运行
const ws = new WebSocket('ws://127.0.0.1:8765');
ws.onopen = () => console.log('连接成功');
ws.onmessage = (e) => console.log('收到消息:', e.data);
ws.onerror = (e) => console.log('连接错误:', e);
```

### 测试HTTP API
```bash
# 测试状态接口
curl http://127.0.0.1:8080/status

# 测试触发接口
curl -X POST http://127.0.0.1:8080/trigger \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```

## 🔄 替代方案

如果Python服务器仍然有问题，可以考虑以下替代方案：

### 方案1: 使用Node.js
```javascript
// 简单的Node.js WebSocket服务器
const WebSocket = require('ws');
const wss = new WebSocket.Server({ port: 8765 });

wss.on('connection', function connection(ws) {
  console.log('Chrome扩展已连接');
  
  ws.on('message', function incoming(message) {
    console.log('收到消息:', message);
  });
  
  ws.send(JSON.stringify({
    type: 'register_success',
    message: 'Chrome扩展注册成功'
  }));
});
```

### 方案2: 使用简单的HTTP轮询
回到之前的HTTP轮询方案，虽然延迟较高但更稳定。

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：

1. **Python版本**: `python --version`
2. **操作系统**: Windows/Linux/macOS
3. **完整的错误日志**
4. **运行的具体命令**
5. **环境信息**: 是否使用虚拟环境等

## 🎯 快速解决方案

如果您急需使用，建议：

1. **先使用测试服务器**: `python test_server.py`
2. **确认Chrome扩展能连接**
3. **再逐步升级到完整功能**

这样可以快速验证基本功能是否正常。
