<?php
/**
 * Chrome扩展WebSocket触发器
 * 简化的PHP类，用于在项目中快速集成Chrome扩展触发功能
 */

require_once __DIR__ . '/vendor/autoload.php';

use WebSocket\Client;
use WebSocket\ConnectionException;

class ChromeExtensionTrigger {
    private $serverUrl;
    private $projectName;
    private $timeout;
    
    public function __construct($serverUrl = 'ws://127.0.0.1:9998', $projectName = 'php_project', $timeout = 10) {
        $this->serverUrl = $serverUrl;
        $this->projectName = $projectName;
        $this->timeout = $timeout;
    }
    
    /**
     * 触发Chrome扩展执行点击操作
     * 
     * @param array $data 附加数据
     * @return array 结果
     */
    public function triggerClick($data = []) {
        try {
            $client = new Client($this->serverUrl, [
                'timeout' => $this->timeout,
                'headers' => ['User-Agent' => 'PHP-Chrome-Extension-Trigger']
            ]);
            
            // 连接并发送身份信息
            $client->send(json_encode([
                'type' => 'connection',
                'client' => 'php_project',
                'project_name' => $this->projectName,
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            
            // 等待连接确认
            $response = $client->receive();
            $connectionData = json_decode($response, true);
            
            if (!$connectionData || $connectionData['type'] !== 'connection_confirmed') {
                throw new Exception('连接确认失败');
            }
            
            // 发送触发消息
            $client->send(json_encode([
                'type' => 'trigger_click',
                'data' => array_merge([
                    'timestamp' => date('Y-m-d H:i:s'),
                    'source' => $this->projectName
                ], $data)
            ]));
            
            // 等待服务器确认
            $response = $client->receive();
            $triggerData = json_decode($response, true);
            
            $client->close();
            
            if ($triggerData && $triggerData['type'] === 'trigger_sent') {
                return [
                    'success' => true,
                    'message_id' => $triggerData['message_id'],
                    'sent_to' => $triggerData['sent_to'],
                    'timestamp' => $triggerData['timestamp']
                ];
            } else {
                throw new Exception('触发确认失败');
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 触发并等待执行完成
     * 
     * @param array $data 附加数据
     * @param int $waitTimeout 等待超时时间（秒）
     * @return array 结果
     */
    public function triggerAndWait($data = [], $waitTimeout = 30) {
        try {
            $client = new Client($this->serverUrl, [
                'timeout' => $this->timeout,
                'headers' => ['User-Agent' => 'PHP-Chrome-Extension-Trigger']
            ]);
            
            // 连接并发送身份信息
            $client->send(json_encode([
                'type' => 'connection',
                'client' => 'php_project',
                'project_name' => $this->projectName,
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            
            // 等待连接确认
            $response = $client->receive();
            $connectionData = json_decode($response, true);
            
            if (!$connectionData || $connectionData['type'] !== 'connection_confirmed') {
                throw new Exception('连接确认失败');
            }
            
            // 发送触发消息
            $client->send(json_encode([
                'type' => 'trigger_click',
                'data' => array_merge([
                    'timestamp' => date('Y-m-d H:i:s'),
                    'source' => $this->projectName
                ], $data)
            ]));
            
            // 等待服务器确认
            $response = $client->receive();
            $triggerData = json_decode($response, true);
            
            if (!$triggerData || $triggerData['type'] !== 'trigger_sent') {
                throw new Exception('触发确认失败');
            }
            
            $messageId = $triggerData['message_id'];
            
            // 等待执行确认
            $startTime = time();
            while (time() - $startTime < $waitTimeout) {
                $client->setTimeout(1); // 1秒超时
                
                try {
                    $response = $client->receive();
                    $confirmData = json_decode($response, true);
                    
                    if ($confirmData && 
                        $confirmData['type'] === 'execution_confirmed' && 
                        $confirmData['message_id'] === $messageId) {
                        
                        $client->close();
                        
                        return [
                            'success' => true,
                            'message_id' => $messageId,
                            'extension_id' => $confirmData['extension_id'],
                            'timestamp' => $confirmData['timestamp'],
                            'sent_to' => $triggerData['sent_to']
                        ];
                    }
                } catch (ConnectionException $e) {
                    // 超时继续等待
                    if (strpos($e->getMessage(), 'timeout') === false) {
                        throw $e;
                    }
                }
            }
            
            $client->close();
            
            return [
                'success' => false,
                'error' => '等待执行确认超时',
                'message_id' => $messageId
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 测试WebSocket连接
     * 
     * @return bool 连接是否正常
     */
    public function testConnection() {
        try {
            $client = new Client($this->serverUrl, [
                'timeout' => 5,
                'headers' => ['User-Agent' => 'PHP-Chrome-Extension-Test']
            ]);
            
            $client->send(json_encode([
                'type' => 'ping',
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            
            $response = $client->receive();
            $data = json_decode($response, true);
            
            $client->close();
            
            return $data && $data['type'] === 'pong';
        } catch (Exception $e) {
            return false;
        }
    }
}

// ==================== 使用示例 ====================

// 示例1: 基本使用
/*
$trigger = new ChromeExtensionTrigger();

$result = $trigger->triggerClick([
    'user_id' => 123,
    'action' => 'process_user'
]);

if ($result['success']) {
    echo "触发成功！消息ID: " . $result['message_id'];
} else {
    echo "触发失败: " . $result['error'];
}
*/

// 示例2: 触发并等待完成
/*
$trigger = new ChromeExtensionTrigger();

$result = $trigger->triggerAndWait([
    'order_id' => 'ORDER-2024-001',
    'priority' => 'high'
], 30);

if ($result['success']) {
    echo "执行完成！扩展ID: " . $result['extension_id'];
} else {
    echo "执行失败: " . $result['error'];
}
*/

// 示例3: 测试连接
/*
$trigger = new ChromeExtensionTrigger();

if ($trigger->testConnection()) {
    echo "WebSocket连接正常";
} else {
    echo "WebSocket连接失败";
}
*/

// 示例4: 在实际项目中使用
/*
function onUserRegistered($userId, $userData) {
    $trigger = new ChromeExtensionTrigger('ws://127.0.0.1:9998', 'my_project');
    
    $result = $trigger->triggerClick([
        'event' => 'user_registered',
        'user_id' => $userId,
        'user_data' => $userData
    ]);
    
    if ($result['success']) {
        error_log("Chrome extension triggered for user: $userId");
        return true;
    } else {
        error_log("Failed to trigger Chrome extension: " . $result['error']);
        return false;
    }
}
*/
?>
