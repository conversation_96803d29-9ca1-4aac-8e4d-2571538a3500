<?php
/**
 * PHP WebSocket客户端
 * 用于PHP项目通过WebSocket与Chrome扩展通信
 * 
 * 依赖: composer require textalk/websocket-client
 */

require_once __DIR__ . '/vendor/autoload.php';

use WebSocket\Client;
use WebSocket\ConnectionException;

class ChromeExtensionWebSocketClient {
    private $client;
    private $serverUrl;
    private $connected = false;
    private $projectName;
    
    public function __construct($serverUrl = 'ws://127.0.0.1:9998', $projectName = 'php_project') {
        $this->serverUrl = $serverUrl;
        $this->projectName = $projectName;
    }
    
    /**
     * 连接到WebSocket服务器
     */
    public function connect() {
        try {
            $this->client = new Client($this->serverUrl, [
                'timeout' => 10,
                'headers' => [
                    'User-Agent' => 'PHP-WebSocket-Client'
                ]
            ]);
            
            // 发送连接消息
            $this->client->send(json_encode([
                'type' => 'connection',
                'client' => 'php_project',
                'project_name' => $this->projectName,
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            
            // 等待连接确认
            $response = $this->client->receive();
            $data = json_decode($response, true);
            
            if ($data && $data['type'] === 'connection_confirmed') {
                $this->connected = true;
                echo "✅ WebSocket连接成功\n";
                echo "🔗 服务器时间: " . $data['server_time'] . "\n";
                echo "📱 客户端ID: " . $data['client_id'] . "\n";
                echo "🔌 Chrome扩展数量: " . $data['chrome_extensions_count'] . "\n";
                return true;
            } else {
                throw new Exception('连接确认失败');
            }
            
        } catch (Exception $e) {
            echo "❌ WebSocket连接失败: " . $e->getMessage() . "\n";
            $this->connected = false;
            return false;
        }
    }
    
    /**
     * 断开连接
     */
    public function disconnect() {
        if ($this->client) {
            $this->client->close();
            $this->connected = false;
            echo "🔌 WebSocket连接已断开\n";
        }
    }
    
    /**
     * 触发Chrome扩展执行点击操作
     */
    public function triggerClick($data = []) {
        if (!$this->connected) {
            return [
                'success' => false,
                'error' => 'WebSocket未连接'
            ];
        }
        
        try {
            $message = [
                'type' => 'trigger_click',
                'data' => array_merge([
                    'timestamp' => date('Y-m-d H:i:s'),
                    'source' => $this->projectName
                ], $data)
            ];
            
            $this->client->send(json_encode($message));
            echo "📤 已发送触发消息\n";
            
            // 等待服务器确认
            $response = $this->client->receive();
            $responseData = json_decode($response, true);
            
            if ($responseData && $responseData['type'] === 'trigger_sent') {
                echo "✅ 服务器确认: 已发送到 " . $responseData['sent_to'] . " 个Chrome扩展\n";
                return [
                    'success' => true,
                    'message_id' => $responseData['message_id'],
                    'sent_to' => $responseData['sent_to']
                ];
            } else {
                throw new Exception('服务器响应异常');
            }
            
        } catch (Exception $e) {
            echo "❌ 发送触发消息失败: " . $e->getMessage() . "\n";
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 等待执行确认
     */
    public function waitForConfirmation($messageId, $timeout = 30) {
        if (!$this->connected) {
            return [
                'success' => false,
                'error' => 'WebSocket未连接'
            ];
        }
        
        $startTime = time();
        echo "⏳ 等待执行确认 (消息ID: $messageId)...\n";
        
        try {
            while (time() - $startTime < $timeout) {
                // 设置接收超时为1秒，避免长时间阻塞
                $this->client->setTimeout(1);
                
                try {
                    $response = $this->client->receive();
                    $data = json_decode($response, true);
                    
                    if ($data && $data['type'] === 'execution_confirmed' && $data['message_id'] === $messageId) {
                        echo "✅ 收到执行确认\n";
                        echo "🔌 来自扩展: " . $data['extension_id'] . "\n";
                        echo "⏰ 确认时间: " . $data['timestamp'] . "\n";
                        
                        return [
                            'success' => true,
                            'message_id' => $messageId,
                            'extension_id' => $data['extension_id'],
                            'timestamp' => $data['timestamp']
                        ];
                    }
                } catch (ConnectionException $e) {
                    // 超时是正常的，继续等待
                    if (strpos($e->getMessage(), 'timeout') === false) {
                        throw $e;
                    }
                }
            }
            
            return [
                'success' => false,
                'error' => '等待确认超时'
            ];
            
        } catch (Exception $e) {
            echo "❌ 等待确认失败: " . $e->getMessage() . "\n";
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 触发并等待确认
     */
    public function triggerAndWait($data = [], $timeout = 30) {
        // 先触发
        $triggerResult = $this->triggerClick($data);
        if (!$triggerResult['success']) {
            return $triggerResult;
        }
        
        // 等待确认
        return $this->waitForConfirmation($triggerResult['message_id'], $timeout);
    }
    
    /**
     * 发送ping测试连接
     */
    public function ping() {
        if (!$this->connected) {
            return false;
        }
        
        try {
            $this->client->send(json_encode([
                'type' => 'ping',
                'timestamp' => date('Y-m-d H:i:s')
            ]));
            
            $response = $this->client->receive();
            $data = json_decode($response, true);
            
            return $data && $data['type'] === 'pong';
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 检查连接状态
     */
    public function isConnected() {
        return $this->connected && $this->ping();
    }
}

// ==================== 使用示例 ====================

if (php_sapi_name() === 'cli') {
    echo "🚀 Chrome扩展WebSocket客户端测试\n";
    echo "==================================\n\n";
    
    $client = new ChromeExtensionWebSocketClient();
    
    // 连接到WebSocket服务器
    echo "📡 连接到WebSocket服务器...\n";
    if (!$client->connect()) {
        echo "❌ 连接失败，请确保WebSocket服务器已启动\n";
        echo "💡 启动命令: php websocket-server.php\n";
        exit(1);
    }
    
    echo "\n";
    
    // 测试ping
    echo "🏓 测试连接...\n";
    if ($client->ping()) {
        echo "✅ 连接正常\n";
    } else {
        echo "❌ 连接异常\n";
    }
    
    echo "\n";
    
    // 示例1: 简单触发
    echo "📝 示例1: 简单触发\n";
    echo "-------------------\n";
    $result = $client->triggerClick([
        'action' => 'test_trigger',
        'user_id' => 123
    ]);
    
    if ($result['success']) {
        echo "✅ 触发成功！消息ID: " . $result['message_id'] . "\n";
    } else {
        echo "❌ 触发失败: " . $result['error'] . "\n";
    }
    
    echo "\n";
    
    // 示例2: 触发并等待确认
    echo "📝 示例2: 触发并等待确认\n";
    echo "------------------------\n";
    $result = $client->triggerAndWait([
        'action' => 'process_order',
        'order_id' => 'ORDER-2024-001',
        'priority' => 'high'
    ], 30);
    
    if ($result['success']) {
        echo "🎉 执行完成！\n";
        echo "📊 扩展ID: " . $result['extension_id'] . "\n";
        echo "⏰ 完成时间: " . $result['timestamp'] . "\n";
    } else {
        echo "❌ 执行失败: " . $result['error'] . "\n";
    }
    
    echo "\n";
    
    // 断开连接
    $client->disconnect();
    echo "🎉 测试完成！\n";
    
} else {
    // Web模式 - 提供简单的Web界面
    header('Content-Type: text/html; charset=utf-8');
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>WebSocket客户端测试</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
            .result { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0; white-space: pre-wrap; }
        </style>
    </head>
    <body>
        <h1>🔗 WebSocket客户端测试</h1>
        
        <?php
        if (isset($_POST['action'])) {
            echo '<div class="result">';
            
            $client = new ChromeExtensionWebSocketClient();
            
            if ($client->connect()) {
                switch ($_POST['action']) {
                    case 'trigger':
                        $result = $client->triggerClick(['test' => true]);
                        echo "触发结果:\n" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                        break;
                        
                    case 'trigger_and_wait':
                        $result = $client->triggerAndWait(['test' => true], 30);
                        echo "触发并等待结果:\n" . json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                        break;
                }
                
                $client->disconnect();
            } else {
                echo "连接失败，请确保WebSocket服务器已启动";
            }
            
            echo '</div>';
        }
        ?>
        
        <form method="post">
            <button type="submit" name="action" value="trigger">触发点击</button>
            <button type="submit" name="action" value="trigger_and_wait">触发并等待</button>
        </form>
        
        <h2>📝 使用说明</h2>
        <ol>
            <li>启动WebSocket服务器: <code>php websocket-server.php</code></li>
            <li>确保Chrome扩展已安装并启用</li>
            <li>点击上面的按钮测试功能</li>
        </ol>
    </body>
    </html>
    <?php
}
?>
