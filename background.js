// 目标URL列表
const TARGET_URLS = [
  'https://mp.weixin.qq.com/cgi-bin/getimgdata',
  'https://mp.weixin.qq.com/cgi-bin/singlesendpage'
];

// 存储待匹配的请求
let pendingRequests = {
  getimgdata: new Map(), // msgid -> {url, timestamp}
  singlesendpage: new Map() // lastmsgid -> {url, tofakeid, timestamp}
};

// 缓存已下载的msgid (10分钟缓存)
let downloadCache = new Map(); // msgid -> {timestamp, tofakeid}
const CACHE_DURATION = 10 * 60 * 1000; // 10分钟 (毫秒)

// 监听网络请求
chrome.webRequest.onBeforeRequest.addListener(
  function(details) {
    // 检查是否是目标URL
    const isTargetUrl = TARGET_URLS.some(url => details.url.includes(url));
    
    if (isTargetUrl) {
      let urlType = '未知请求';
      if (details.url.includes('getimgdata')) {
        urlType = '图片数据包';
      } else if (details.url.includes('singlesendpage')) {
        urlType = '单发页面';
      }
      
      console.log(`检测到目标${urlType}请求:`, details.url);
      console.log('请求方法:', details.method);
      console.log('请求类型:', details.type);
      console.log('请求ID:', details.requestId);
      

      
      // 发送消息到popup页面
      chrome.runtime.sendMessage({
        type: 'TARGET_DETECTED',
        url: details.url,
        urlType: urlType,
        method: details.method,
        timestamp: new Date().toISOString()
      });
      
      // 处理不同类型的请求
      if (details.url.includes('getimgdata')) {
        handleGetImgData(details.url);
      } else if (details.url.includes('singlesendpage')) {
        handleSingleSendPage(details.url);
      }
    }
  },
  {
    urls: [
      "https://mp.weixin.qq.com/cgi-bin/getimgdata*",
      "https://mp.weixin.qq.com/cgi-bin/singlesendpage*"
    ]
  },
  ["requestBody"]  // 添加requestBody以获取POST请求的内容
);

// 监听响应完成事件
chrome.webRequest.onCompleted.addListener(
  function(details) {
    const isTargetUrl = TARGET_URLS.some(url => details.url.includes(url));
    if (isTargetUrl) {
      let urlType = '未知请求';
      if (details.url.includes('getimgdata')) {
        urlType = '图片数据包';
      } else if (details.url.includes('singlesendpage')) {
        urlType = '单发页面';
      }
      
      // 其他类型的请求
      console.log(`${urlType}请求完成:`, details.url);
      console.log('响应状态码:', details.statusCode);
      console.log('响应大小:', details.responseSize);
    }
  },
  {
    urls: [
      "https://mp.weixin.qq.com/cgi-bin/getimgdata*",
      "https://mp.weixin.qq.com/cgi-bin/singlesendpage*"
    ]
  },
  ["responseHeaders"]  // 添加responseHeaders以获取响应头
);



// 监听扩展安装事件
chrome.runtime.onInstalled.addListener(function() {
  console.log('WeChat Monitor 插件已安装');
  console.log('开始监听以下URL:');
  TARGET_URLS.forEach(url => console.log('-', url));
  console.log('📝 下载模式: 覆盖模式 (conflictAction: overwrite)');
});

// 检查缓存是否有效
function isCacheValid(msgid) {
  const cacheEntry = downloadCache.get(msgid);
  if (!cacheEntry) return false;
  
  const now = Date.now();
  const cacheAge = now - cacheEntry.timestamp;
  
  if (cacheAge > CACHE_DURATION) {
    // 缓存过期，删除
    downloadCache.delete(msgid);
    console.log(`⏰ 缓存过期，删除msgid: ${msgid}`);
    return false;
  }
  
  return true;
}

// 清理过期缓存
function cleanupExpiredCache() {
  const now = Date.now();
  let cleanedCount = 0;
  
  for (const [msgid, cacheEntry] of downloadCache.entries()) {
    const cacheAge = now - cacheEntry.timestamp;
    if (cacheAge > CACHE_DURATION) {
      downloadCache.delete(msgid);
      cleanedCount++;
    }
  }
  
  if (cleanedCount > 0) {
    console.log(`🧹 清理了 ${cleanedCount} 个过期缓存`);
  }
}

// 定期清理旧的下载记录 (可选功能)
function cleanupOldDownloads() {
  // 这里可以添加清理逻辑，比如删除超过30天的下载记录
  // 由于Chrome扩展的限制，我们只能管理下载记录，不能直接删除文件
  console.log('🧹 定期清理: 下载记录已优化');
}

// 处理getimgdata请求
function handleGetImgData(url) {
  const urlParams = new URLSearchParams(url.split('?')[1] || '');
  const msgid = urlParams.get('msgid');
  
  if (!msgid) {
    console.log('⚠️ getimgdata请求缺少msgid参数');
    return;
  }
  
  console.log('📥 收到getimgdata请求，msgid:', msgid);

  // 存储getimgdata请求
  pendingRequests.getimgdata.set(msgid, {
    url: url,
    timestamp: new Date().toISOString()
  });

  // 检查是否有对应的singlesendpage请求 (msgid 或 msgid+1)
  checkAndDownload(msgid);

  // 同时检查msgid+1的singlesendpage请求
  const nextMsgid = (parseInt(msgid) + 1).toString();
  if (pendingRequests.singlesendpage.has(nextMsgid)) {
    console.log(`🔍 发现msgid+1的singlesendpage请求: getimgdata msgid=${msgid}, singlesendpage lastmsgid=${nextMsgid}`);
    checkAndDownload(nextMsgid);
  }
}

// 处理singlesendpage请求
function handleSingleSendPage(url) {
  const urlParams = new URLSearchParams(url.split('?')[1] || '');
  const lastmsgid = urlParams.get('lastmsgid');
  const tofakeid = urlParams.get('tofakeid');
  
  if (!lastmsgid || !tofakeid) {
    console.log('⚠️ singlesendpage请求缺少必要参数');
    return;
  }
  
  console.log('📤 收到singlesendpage请求，lastmsgid:', lastmsgid, 'tofakeid:', tofakeid);
  
  // 存储singlesendpage请求
  pendingRequests.singlesendpage.set(lastmsgid, {
    url: url,
    tofakeid: tofakeid,
    timestamp: new Date().toISOString()
  });
  
  // 检查是否有对应的getimgdata请求
  checkAndDownload(lastmsgid);
}

// 检查并下载匹配的请求
function checkAndDownload(msgid) {
  // 首先清理过期缓存
  cleanupExpiredCache();
  
  // 检查缓存
  if (isCacheValid(msgid)) {
    const cacheEntry = downloadCache.get(msgid);
    console.log(`⏰ 跳过下载，msgid: ${msgid} 在缓存中 (${cacheEntry.tofakeid})`);
    console.log(`⏰ 缓存剩余时间: ${Math.round((CACHE_DURATION - (Date.now() - cacheEntry.timestamp)) / 1000)}秒`);
    
            // 发送缓存跳过消息到popup
        chrome.runtime.sendMessage({
          type: 'CACHE_SKIP',
          msgid: msgid,
          tofakeid: cacheEntry.tofakeid,
          timestamp: new Date().toISOString(),
          remainingTime: Math.round((CACHE_DURATION - (Date.now() - cacheEntry.timestamp)) / 1000)
        });
        
        // 清理已匹配的请求
        pendingRequests.getimgdata.delete(msgid);
        pendingRequests.singlesendpage.delete(msgid);
        return;
  }
  
  let getimgdataRequest = pendingRequests.getimgdata.get(msgid);
  const singlesendpageRequest = pendingRequests.singlesendpage.get(msgid);

  // 如果没有找到对应的getimgdata请求，尝试使用msgid-1
  let actualMsgid = msgid;
  if (!getimgdataRequest && singlesendpageRequest) {
    const previousMsgid = (parseInt(msgid) - 1).toString();
    getimgdataRequest = pendingRequests.getimgdata.get(previousMsgid);
    if (getimgdataRequest) {
      actualMsgid = previousMsgid;
      console.log(`🔍 使用lastmsgid-1匹配: lastmsgid=${msgid}, getimgdata msgid=${previousMsgid}`);
    }
  }

  if (getimgdataRequest && singlesendpageRequest) {
    console.log('🎯 找到匹配的请求对');
    console.log('📤 singlesendpage lastmsgid:', msgid);
    console.log('📥 getimgdata msgid:', actualMsgid);
    console.log('📥 getimgdata URL:', getimgdataRequest.url);
    console.log('📤 singlesendpage URL:', singlesendpageRequest.url);
    
    // 使用tofakeid命名文件
    const filename = `${singlesendpageRequest.tofakeid}.gif`;
    
    console.log('🔄 开始下载图片，文件名:', filename);
    console.log('🆔 msgid:', msgid);
    console.log('👤 tofakeid:', singlesendpageRequest.tofakeid);
    console.log('📝 覆盖模式: 如果文件已存在将被覆盖');
    
    // 下载图片 - 覆盖模式
    chrome.downloads.download({
      url: getimgdataRequest.url,
      filename: filename,
      saveAs: false,
      conflictAction: 'overwrite' // 覆盖已存在的文件
    }, function(downloadId) {
      if (chrome.runtime.lastError) {
        console.error('❌ 下载失败:', chrome.runtime.lastError.message);
      } else {
        console.log('✅ 图片下载成功，下载ID:', downloadId);
        console.log('📁 文件已保存/覆盖:', filename);
        
        // 添加到缓存
        downloadCache.set(msgid, {
          timestamp: Date.now(),
          tofakeid: singlesendpageRequest.tofakeid
        });
        console.log(`💾 已缓存msgid: ${msgid}, tofakeid: ${singlesendpageRequest.tofakeid}`);

        // 发送HTTP请求到本地API
        sendApiRequest(singlesendpageRequest.tofakeid);

        // 发送下载成功消息到popup
        chrome.runtime.sendMessage({
          type: 'DOWNLOAD_SUCCESS',
          url: getimgdataRequest.url,
          filename: filename,
          msgid: msgid,
          tofakeid: singlesendpageRequest.tofakeid,
          downloadId: downloadId,
          timestamp: new Date().toISOString(),
          overwritten: true,
          cached: false
        });

        // 清理已匹配的请求
        pendingRequests.getimgdata.delete(actualMsgid);
        pendingRequests.singlesendpage.delete(msgid);
      }
    });
  } else {
    console.log('⏳ 等待匹配的请求，msgid:', msgid);
    if (getimgdataRequest) {
      console.log('  - 已有getimgdata请求 (msgid:', actualMsgid, ')');
    } else {
      console.log('  - 缺少getimgdata请求');
      // 检查是否有msgid-1的getimgdata请求
      const previousMsgid = (parseInt(msgid) - 1).toString();
      const previousGetimgdata = pendingRequests.getimgdata.get(previousMsgid);
      if (previousGetimgdata) {
        console.log('  - 发现msgid-1的getimgdata请求:', previousMsgid);
      }
    }
    if (singlesendpageRequest) {
      console.log('  - 已有singlesendpage请求 (lastmsgid:', msgid, ')');
    } else {
      console.log('  - 缺少singlesendpage请求');
    }
  }
}



// 发送API请求到本地服务器
function sendApiRequest(tofakeid) {
  const apiUrl = `http://127.0.0.1/api/send?openid=${tofakeid}`;

  console.log('🌐 发送API请求:', apiUrl);
  console.log('🆔 tofakeid:', tofakeid);

  fetch(apiUrl, {
    method: 'GET',
    mode: 'cors',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json, text/plain, */*'
    }
  })
  .then(response => {
    console.log('📡 API响应状态:', response.status);
    console.log('📡 API响应状态文本:', response.statusText);

    if (response.ok) {
      console.log('✅ API请求成功');
      return response.text();
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  })
  .then(data => {
    console.log('📄 API响应内容:', data);

    // 发送成功消息到popup
    try {
      chrome.runtime.sendMessage({
        type: 'API_REQUEST_SUCCESS',
        tofakeid: tofakeid,
        apiUrl: apiUrl,
        response: data,
        timestamp: new Date().toISOString()
      });
    } catch (e) {
      console.log('❌ 发送API成功消息失败:', e);
    }
  })
  .catch(error => {
    console.error('❌ API请求失败:', error);

    // 发送失败消息到popup
    try {
      chrome.runtime.sendMessage({
        type: 'API_REQUEST_FAILED',
        tofakeid: tofakeid,
        apiUrl: apiUrl,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } catch (e) {
      console.log('❌ 发送API失败消息失败:', e);
    }
  });
}

// WebSocket连接管理
let wsConnection = null;
let wsReconnectAttempts = 0;
const maxReconnectAttempts = 10;
const reconnectDelay = 5000; // 5秒

// Python WebSocket监听服务
function startPythonWebSocketListener() {
  console.log('🚀 启动Python WebSocket监听服务...');

  function connectWebSocket() {
    try {
      // 连接到Python WebSocket服务器
      wsConnection = new WebSocket('ws://127.0.0.1:8765');

      wsConnection.onopen = function(event) {
        console.log('✅ WebSocket连接已建立');
        console.log('🔗 连接地址: ws://127.0.0.1:8765');
        wsReconnectAttempts = 0;

        // 发送连接确认消息
        wsConnection.send(JSON.stringify({
          type: 'register',
          client_type: 'chrome_extension',
          extension_id: chrome.runtime.id,
          timestamp: new Date().toISOString()
        }));
      };

      wsConnection.onmessage = function(event) {
        try {
          const message = JSON.parse(event.data);
          console.log('📡 收到Python WebSocket消息:', message);

          // 处理触发点击序列的消息
          if (message.type === 'execute_click_sequence') {
            console.log('🎯 收到Python服务器的触发请求');
            console.log('📡 请求数据:', message.data);
            console.log('📡 请求ID:', message.request_id);

            // 向所有标签页发送执行点击序列的消息
            chrome.tabs.query({}, function(tabs) {
              let sentCount = 0;
              tabs.forEach(tab => {
                try {
                  chrome.tabs.sendMessage(tab.id, {
                    type: 'EXECUTE_CLICK_SEQUENCE',
                    source: 'python_websocket',
                    data: message.data,
                    request_id: message.request_id,
                    timestamp: new Date().toISOString()
                  });
                  sentCount++;
                  console.log('📤 已发送点击序列执行消息到标签页:', tab.id);
                } catch (error) {
                  console.log('❌ 发送消息到标签页失败:', error);
                }
              });
              console.log(`📊 总共发送到 ${sentCount} 个标签页`);
            });

            // 发送执行确认
            if (wsConnection.readyState === WebSocket.OPEN) {
              wsConnection.send(JSON.stringify({
                type: 'execution_started',
                request_id: message.request_id,
                status: 'started',
                timestamp: new Date().toISOString(),
                extension_id: chrome.runtime.id
              }));
              console.log('✅ 已通过WebSocket发送执行开始确认');
            }
          }
          // 处理ping消息
          else if (message.type === 'ping') {
            if (wsConnection.readyState === WebSocket.OPEN) {
              wsConnection.send(JSON.stringify({
                type: 'pong',
                timestamp: new Date().toISOString()
              }));
            }
          }
          // 处理连接确认
          else if (message.type === 'register_success') {
            console.log('✅ Chrome扩展注册成功');
          }
        } catch (error) {
          console.log('❌ 处理WebSocket消息失败:', error);
        }
      };

      wsConnection.onclose = function(event) {
        console.log('🔌 WebSocket连接已关闭');
        console.log('🔌 关闭代码:', event.code, '原因:', event.reason);
        wsConnection = null;

        // 尝试重连
        if (wsReconnectAttempts < maxReconnectAttempts) {
          wsReconnectAttempts++;
          console.log(`🔄 尝试重连 WebSocket (${wsReconnectAttempts}/${maxReconnectAttempts})...`);
          setTimeout(connectWebSocket, reconnectDelay);
        } else {
          console.log('❌ WebSocket重连次数已达上限，停止重连');
        }
      };

      wsConnection.onerror = function(error) {
        console.log('❌ WebSocket连接错误:', error);
      };

    } catch (error) {
      console.log('❌ 创建WebSocket连接失败:', error);

      // 尝试重连
      if (wsReconnectAttempts < maxReconnectAttempts) {
        wsReconnectAttempts++;
        console.log(`🔄 尝试重连 WebSocket (${wsReconnectAttempts}/${maxReconnectAttempts})...`);
        setTimeout(connectWebSocket, reconnectDelay);
      }
    }
  }

  // 初始连接
  connectWebSocket();

  // 定期检查连接状态
  setInterval(() => {
    if (!wsConnection || wsConnection.readyState === WebSocket.CLOSED) {
      if (wsReconnectAttempts < maxReconnectAttempts) {
        console.log('🔄 检测到WebSocket连接断开，尝试重连...');
        connectWebSocket();
      }
    } else if (wsConnection.readyState === WebSocket.OPEN) {
      // 发送心跳包
      wsConnection.send(JSON.stringify({
        type: 'heartbeat',
        timestamp: new Date().toISOString()
      }));
    }
  }, 30000); // 每30秒检查一次

  console.log('✅ Python WebSocket监听服务已启动');
  console.log('🔗 WebSocket地址: ws://127.0.0.1:8765');
  console.log('💓 心跳检查: 每30秒一次');
}

// 发送执行完成确认到Python服务器
function sendExecutionComplete(requestId, success, error = null) {
  if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
    wsConnection.send(JSON.stringify({
      type: 'execution_complete',
      request_id: requestId,
      success: success,
      error: error,
      timestamp: new Date().toISOString(),
      extension_id: chrome.runtime.id
    }));
    console.log('✅ 已发送执行完成确认到Python服务器');
  }
}

// 启动Python WebSocket监听服务
startPythonWebSocketListener();

// 处理来自popup和content的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.type === 'GET_STATUS') {
    sendResponse({
      status: 'active',
      targetUrls: TARGET_URLS
    });
  } else if (request.type === 'MANUAL_DOWNLOAD') {
    // 手动下载请求
    autoDownloadImage(request.url);
    sendResponse({ success: true });

  } else if (request.type === 'FIRST_ELEMENT_CLICKED') {
    // 处理第一个元素点击消息
    console.log('🎯 收到第一个元素点击消息');
    console.log('🎯 选择器:', request.selector);
    console.log('🎯 时间戳:', request.timestamp);

    // 发送消息到popup
    try {
      chrome.runtime.sendMessage({
        type: 'FIRST_ELEMENT_CLICKED_RECEIVED',
        selector: request.selector,
        timestamp: request.timestamp
      });
    } catch (e) {
      console.log('❌ 发送第一个元素点击消息到popup失败:', e);
    }

    sendResponse({ success: true });
  } else if (request.type === 'EXECUTION_COMPLETE') {
    // 处理来自content.js的执行完成消息
    console.log('📨 收到content.js的执行完成消息');
    console.log('📡 请求ID:', request.request_id);
    console.log('✅ 执行结果:', request.success ? '成功' : '失败');

    // 转发执行完成消息到Python服务器
    sendExecutionComplete(request.request_id, request.success, request.error);

    sendResponse({ success: true });
  } else if (request.type === 'SECOND_ELEMENT_CLICKED') {
    // 处理第二个元素点击消息
    console.log('🎯 收到第二个元素点击消息');
    console.log('🎯 选择器:', request.selector);
    console.log('🎯 时间戳:', request.timestamp);

    // 发送消息到popup
    try {
      chrome.runtime.sendMessage({
        type: 'SECOND_ELEMENT_CLICKED_RECEIVED',
        selector: request.selector,
        timestamp: request.timestamp
      });
    } catch (e) {
      console.log('❌ 发送第二个元素点击消息到popup失败:', e);
    }

    sendResponse({ success: true });
  }
});