// 目标URL列表
const TARGET_URLS = [
  'https://mp.weixin.qq.com/cgi-bin/getimgdata',
  'https://mp.weixin.qq.com/cgi-bin/singlesendpage',
  'https://mp.weixin.qq.com/cgi-bin/getnewmsgnum'
];

// 存储待匹配的请求
let pendingRequests = {
  getimgdata: new Map(), // msgid -> {url, timestamp}
  singlesendpage: new Map() // lastmsgid -> {url, tofakeid, timestamp}
};

// 缓存已下载的msgid (10分钟缓存)
let downloadCache = new Map(); // msgid -> {timestamp, tofakeid}
const CACHE_DURATION = 10 * 60 * 1000; // 10分钟 (毫秒)

// 监听网络请求
chrome.webRequest.onBeforeRequest.addListener(
  function(details) {
    // 检查是否是目标URL
    const isTargetUrl = TARGET_URLS.some(url => details.url.includes(url));
    
    if (isTargetUrl) {
      let urlType = '未知请求';
      if (details.url.includes('getimgdata')) {
        urlType = '图片数据包';
      } else if (details.url.includes('singlesendpage')) {
        urlType = '单发页面';
      } else if (details.url.includes('getnewmsgnum')) {
        urlType = '新消息数量';
      }
      
      console.log(`检测到目标${urlType}请求:`, details.url);
      console.log('请求方法:', details.method);
      console.log('请求类型:', details.type);
      console.log('请求ID:', details.requestId);
      
      // 如果是getnewmsgnum，打印更多信息
      if (details.url.includes('getnewmsgnum')) {
        console.log('📊 ===== getnewmsgnum请求检测 =====');
        console.log('📊 请求URL:', details.url);
        console.log('📊 请求方法:', details.method);
        console.log('📊 请求类型:', details.type);
        console.log('📊 请求ID:', details.requestId);
        console.log('📊 时间戳:', new Date().toISOString());
        
        // 如果是POST请求，尝试获取请求体
        if (details.method === 'POST') {
          console.log('📊 这是一个POST请求');
          if (details.requestBody) {
            console.log('📊 请求体:', details.requestBody);
            if (details.requestBody.formData) {
              console.log('📊 表单数据:', details.requestBody.formData);
            }
          }
        }
        console.log('📊 ===== getnewmsgnum请求检测结束 =====');
      }
      
      // 发送消息到popup页面
      chrome.runtime.sendMessage({
        type: 'TARGET_DETECTED',
        url: details.url,
        urlType: urlType,
        method: details.method,
        timestamp: new Date().toISOString()
      });
      
      // 处理不同类型的请求
      if (details.url.includes('getimgdata')) {
        handleGetImgData(details.url);
      } else if (details.url.includes('singlesendpage')) {
        handleSingleSendPage(details.url);
      } else if (details.url.includes('getnewmsgnum')) {
        handleGetNewMsgNum(details.url);
      }
    }
  },
  {
    urls: [
      "https://mp.weixin.qq.com/cgi-bin/getimgdata*",
      "https://mp.weixin.qq.com/cgi-bin/singlesendpage*",
      "https://mp.weixin.qq.com/cgi-bin/getnewmsgnum*"
    ]
  },
  ["requestBody"]  // 添加requestBody以获取POST请求的内容
);

// 监听响应完成事件
chrome.webRequest.onCompleted.addListener(
  function(details) {
    const isTargetUrl = TARGET_URLS.some(url => details.url.includes(url));
    if (isTargetUrl) {
      let urlType = '未知请求';
      if (details.url.includes('getimgdata')) {
        urlType = '图片数据包';
      } else if (details.url.includes('singlesendpage')) {
        urlType = '单发页面';
      } else if (details.url.includes('getnewmsgnum')) {
        urlType = '新消息数量';
      }
      
      // 如果是getnewmsgnum，打印详细信息
      if (details.url.includes('getnewmsgnum')) {
        console.log('📊 ===== getnewmsgnum响应完成 =====');
        console.log('📊 请求URL:', details.url);
        console.log('📊 请求方法:', details.method);
        console.log('📊 响应状态码:', details.statusCode);
        console.log('📊 响应大小:', details.responseSize);
        console.log('📊 时间戳:', new Date().toISOString());
        console.log('📊 请求ID:', details.requestId);
        
        if (details.responseHeaders) {
          console.log('📊 响应头:');
          details.responseHeaders.forEach(header => {
            console.log(`📊 ${header.name}: ${header.value}`);
          });
        }
        
        console.log('📊 ===== getnewmsgnum响应完成 =====');
        
        // 通知所有标签页处理响应
        chrome.tabs.query({}, function(tabs) {
          console.log(`📊 发送响应通知到 ${tabs.length} 个标签页`);
          tabs.forEach(tab => {
            try {
              chrome.tabs.sendMessage(tab.id, {
                type: 'GETNEWMSGNUM_COMPLETED',
                url: details.url,
                method: details.method,
                statusCode: details.statusCode,
                responseSize: details.responseSize,
                timestamp: Date.now()
              });
              console.log('📊 已发送GETNEWMSGNUM_COMPLETED消息到标签页:', tab.id);
            } catch (error) {
              console.log('❌ 发送消息到标签页失败:', error);
            }
          });
        });
      } else {
        // 其他类型的请求
        console.log(`${urlType}请求完成:`, details.url);
        console.log('响应状态码:', details.statusCode);
        console.log('响应大小:', details.responseSize);
      }
    }
  },
  {
    urls: [
      "https://mp.weixin.qq.com/cgi-bin/getimgdata*",
      "https://mp.weixin.qq.com/cgi-bin/singlesendpage*",
      "https://mp.weixin.qq.com/cgi-bin/getnewmsgnum*"
    ]
  },
  ["responseHeaders"]  // 添加responseHeaders以获取响应头
);

// 跟踪已处理的请求，避免重复处理
const processedRequests = new Set();

// 尝试直接获取getnewmsgnum响应内容
function fetchGetNewMsgNumResponse(url) {
  // 检查是否已经处理过此请求
  const requestKey = url.split('?')[0] + Date.now().toString().substring(0, 8);
  if (processedRequests.has(requestKey)) {
    console.log('📊 已处理过类似请求，跳过:', url);
    return;
  }
  
  // 将请求标记为已处理
  processedRequests.add(requestKey);
  
  // 5秒后从集合中删除，允许将来处理类似请求
  setTimeout(() => {
    processedRequests.delete(requestKey);
  }, 5000);
  
  console.log('📊 尝试直接获取getnewmsgnum响应内容:', url);
  
  // 使用原始URL，不添加随机参数
  console.log('📊 分析原始响应:', url);
  
  // 不再发起新的fetch请求，只记录分析
  console.log('📊 ===== getnewmsgnum响应分析 =====');
  console.log('📊 请求URL:', url);
  console.log('📊 分析时间:', new Date().toISOString());
  console.log('📊 ===== getnewmsgnum响应分析结束 =====');
}

// 通知content script点击新消息元素
function notifyContentScriptToClick(count) {
  console.log('🔔 通知content script点击新消息元素, 数量:', count);
  
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    if (tabs[0]) {
      chrome.tabs.sendMessage(tabs[0].id, {
        type: 'CLICK_NEW_MESSAGE',
        count: count,
        timestamp: Date.now()
      });
      console.log('🔔 已发送CLICK_NEW_MESSAGE消息到标签页:', tabs[0].id);
    } else {
      console.log('❌ 未找到活动标签页');
    }
  });
}

// 监听扩展安装事件
chrome.runtime.onInstalled.addListener(function() {
  console.log('WeChat Monitor 插件已安装');
  console.log('开始监听以下URL:');
  TARGET_URLS.forEach(url => console.log('-', url));
  console.log('📝 下载模式: 覆盖模式 (conflictAction: overwrite)');
});

// 检查缓存是否有效
function isCacheValid(msgid) {
  const cacheEntry = downloadCache.get(msgid);
  if (!cacheEntry) return false;
  
  const now = Date.now();
  const cacheAge = now - cacheEntry.timestamp;
  
  if (cacheAge > CACHE_DURATION) {
    // 缓存过期，删除
    downloadCache.delete(msgid);
    console.log(`⏰ 缓存过期，删除msgid: ${msgid}`);
    return false;
  }
  
  return true;
}

// 清理过期缓存
function cleanupExpiredCache() {
  const now = Date.now();
  let cleanedCount = 0;
  
  for (const [msgid, cacheEntry] of downloadCache.entries()) {
    const cacheAge = now - cacheEntry.timestamp;
    if (cacheAge > CACHE_DURATION) {
      downloadCache.delete(msgid);
      cleanedCount++;
    }
  }
  
  if (cleanedCount > 0) {
    console.log(`🧹 清理了 ${cleanedCount} 个过期缓存`);
  }
}

// 定期清理旧的下载记录 (可选功能)
function cleanupOldDownloads() {
  // 这里可以添加清理逻辑，比如删除超过30天的下载记录
  // 由于Chrome扩展的限制，我们只能管理下载记录，不能直接删除文件
  console.log('🧹 定期清理: 下载记录已优化');
}

// 处理getimgdata请求
function handleGetImgData(url) {
  const urlParams = new URLSearchParams(url.split('?')[1] || '');
  const msgid = urlParams.get('msgid');
  
  if (!msgid) {
    console.log('⚠️ getimgdata请求缺少msgid参数');
    return;
  }
  
  console.log('📥 收到getimgdata请求，msgid:', msgid);
  
  // 存储getimgdata请求
  pendingRequests.getimgdata.set(msgid, {
    url: url,
    timestamp: new Date().toISOString()
  });
  
  // 检查是否有对应的singlesendpage请求
  checkAndDownload(msgid);
}

// 处理singlesendpage请求
function handleSingleSendPage(url) {
  const urlParams = new URLSearchParams(url.split('?')[1] || '');
  const lastmsgid = urlParams.get('lastmsgid');
  const tofakeid = urlParams.get('tofakeid');
  
  if (!lastmsgid || !tofakeid) {
    console.log('⚠️ singlesendpage请求缺少必要参数');
    return;
  }
  
  console.log('📤 收到singlesendpage请求，lastmsgid:', lastmsgid, 'tofakeid:', tofakeid);
  
  // 存储singlesendpage请求
  pendingRequests.singlesendpage.set(lastmsgid, {
    url: url,
    tofakeid: tofakeid,
    timestamp: new Date().toISOString()
  });
  
  // 检查是否有对应的getimgdata请求
  checkAndDownload(lastmsgid);
}

// 检查并下载匹配的请求
function checkAndDownload(msgid) {
  // 首先清理过期缓存
  cleanupExpiredCache();
  
  // 检查缓存
  if (isCacheValid(msgid)) {
    const cacheEntry = downloadCache.get(msgid);
    console.log(`⏰ 跳过下载，msgid: ${msgid} 在缓存中 (${cacheEntry.tofakeid})`);
    console.log(`⏰ 缓存剩余时间: ${Math.round((CACHE_DURATION - (Date.now() - cacheEntry.timestamp)) / 1000)}秒`);
    
            // 发送缓存跳过消息到popup
        chrome.runtime.sendMessage({
          type: 'CACHE_SKIP',
          msgid: msgid,
          tofakeid: cacheEntry.tofakeid,
          timestamp: new Date().toISOString(),
          remainingTime: Math.round((CACHE_DURATION - (Date.now() - cacheEntry.timestamp)) / 1000)
        });
        
        // 清理已匹配的请求
        pendingRequests.getimgdata.delete(msgid);
        pendingRequests.singlesendpage.delete(msgid);
        return;
  }
  
  const getimgdataRequest = pendingRequests.getimgdata.get(msgid);
  const singlesendpageRequest = pendingRequests.singlesendpage.get(msgid);
  
  if (getimgdataRequest && singlesendpageRequest) {
    console.log('🎯 找到匹配的请求对，msgid:', msgid);
    console.log('📥 getimgdata:', getimgdataRequest.url);
    console.log('📤 singlesendpage:', singlesendpageRequest.url);
    
    // 使用tofakeid命名文件
    const filename = `${singlesendpageRequest.tofakeid}.gif`;
    
    console.log('🔄 开始下载图片，文件名:', filename);
    console.log('🆔 msgid:', msgid);
    console.log('👤 tofakeid:', singlesendpageRequest.tofakeid);
    console.log('📝 覆盖模式: 如果文件已存在将被覆盖');
    
    // 下载图片 - 覆盖模式
    chrome.downloads.download({
      url: getimgdataRequest.url,
      filename: filename,
      saveAs: false,
      conflictAction: 'overwrite' // 覆盖已存在的文件
    }, function(downloadId) {
      if (chrome.runtime.lastError) {
        console.error('❌ 下载失败:', chrome.runtime.lastError.message);
      } else {
        console.log('✅ 图片下载成功，下载ID:', downloadId);
        console.log('📁 文件已保存/覆盖:', filename);
        
        // 添加到缓存
        downloadCache.set(msgid, {
          timestamp: Date.now(),
          tofakeid: singlesendpageRequest.tofakeid
        });
        console.log(`💾 已缓存msgid: ${msgid}, tofakeid: ${singlesendpageRequest.tofakeid}`);
        
        // 发送下载成功消息到popup
        chrome.runtime.sendMessage({
          type: 'DOWNLOAD_SUCCESS',
          url: getimgdataRequest.url,
          filename: filename,
          msgid: msgid,
          tofakeid: singlesendpageRequest.tofakeid,
          downloadId: downloadId,
          timestamp: new Date().toISOString(),
          overwritten: true,
          cached: false
        });
        
        // 清理已匹配的请求
        pendingRequests.getimgdata.delete(msgid);
        pendingRequests.singlesendpage.delete(msgid);
      }
    });
  } else {
    console.log('⏳ 等待匹配的请求，msgid:', msgid);
    if (getimgdataRequest) {
      console.log('  - 已有getimgdata请求');
    }
    if (singlesendpageRequest) {
      console.log('  - 已有singlesendpage请求');
    }
  }
}

// 处理getnewmsgnum请求
function handleGetNewMsgNum(url) {
  console.log('📊 ===== 处理getnewmsgnum请求 =====');
  console.log('📊 请求URL:', url);
  console.log('📊 处理时间:', new Date().toISOString());
  
  // 解析URL参数
  try {
    const urlObj = new URL(url);
    console.log('📊 URL参数:', Object.fromEntries(urlObj.searchParams));
  } catch (e) {
    console.log('❌ 解析URL失败:', e);
  }
  
  // 发送消息到content script来处理响应
  chrome.tabs.query({}, function(tabs) {
    console.log(`📊 发送MONITOR_GETNEWMSGNUM消息到 ${tabs.length} 个标签页`);
    
    // 向所有标签页发送消息
    tabs.forEach(tab => {
      try {
        chrome.tabs.sendMessage(tab.id, {
          type: 'MONITOR_GETNEWMSGNUM',
          url: url,
          timestamp: new Date().toISOString()
        });
        console.log('📊 消息已发送到标签页:', tab.id);
      } catch (error) {
        console.log('❌ 发送消息到标签页失败:', error);
      }
    });
  });
  
  // 不发送模拟请求，只监听原始请求
  console.log('📊 getnewmsgnum是POST请求，只监听原始请求，不模拟请求');
  console.log('📊 等待原始请求的响应...');
  
  console.log('📊 ===== getnewmsgnum请求处理完成 =====');
}

// 处理来自popup和content的消息
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.type === 'GET_STATUS') {
    sendResponse({
      status: 'active',
      targetUrls: TARGET_URLS
    });
  } else if (request.type === 'MANUAL_DOWNLOAD') {
    // 手动下载请求
    autoDownloadImage(request.url);
    sendResponse({ success: true });
  } else if (request.type === 'GETNEWMSGNUM_RESPONSE') {
    // 处理getnewmsgnum响应数据
    console.log('📊📊📊 收到getnewmsgnum响应数据 📊📊📊');
    console.log('📊 请求URL:', request.url);
    console.log('📊 请求方法:', request.method);
    console.log('📊 响应状态:', request.status);
    console.log('📊 newTotalMsgCount值:', request.newTotalMsgCount);
    console.log('📊 时间戳:', request.timestamp);
    
    // 存储响应数据
    try {
      chrome.storage.local.get(['getnewmsgnumResponses'], function(result) {
        const responses = result.getnewmsgnumResponses || [];
        responses.unshift({
          url: request.url,
          method: request.method,
          status: request.status,
          newTotalMsgCount: request.newTotalMsgCount,
          timestamp: request.timestamp,
          data: request.data
        });
        
        // 限制存储数量
        if (responses.length > 10) {
          responses.splice(10);
        }
        
        chrome.storage.local.set({ 'getnewmsgnumResponses': responses });
        console.log('📊 已存储getnewmsgnum响应数据');
      });
    } catch (e) {
      console.log('❌ 存储getnewmsgnum响应数据失败:', e);
    }
    
    // 发送消息到popup
    try {
      chrome.runtime.sendMessage({
        type: 'GETNEWMSGNUM_RESPONSE_RECEIVED',
        url: request.url,
        method: request.method,
        status: request.status,
        newTotalMsgCount: request.newTotalMsgCount,
        timestamp: request.timestamp
      });
    } catch (e) {
      console.log('❌ 发送消息到popup失败:', e);
    }

    sendResponse({ success: true });
  } else if (request.type === 'FIRST_ELEMENT_CLICKED') {
    // 处理第一个元素点击消息
    console.log('🎯 收到第一个元素点击消息');
    console.log('🎯 选择器:', request.selector);
    console.log('🎯 时间戳:', request.timestamp);

    // 发送消息到popup
    try {
      chrome.runtime.sendMessage({
        type: 'FIRST_ELEMENT_CLICKED_RECEIVED',
        selector: request.selector,
        timestamp: request.timestamp
      });
    } catch (e) {
      console.log('❌ 发送第一个元素点击消息到popup失败:', e);
    }

    sendResponse({ success: true });
  } else if (request.type === 'SECOND_ELEMENT_CLICKED') {
    // 处理第二个元素点击消息
    console.log('🎯 收到第二个元素点击消息');
    console.log('🎯 选择器:', request.selector);
    console.log('🎯 时间戳:', request.timestamp);

    // 发送消息到popup
    try {
      chrome.runtime.sendMessage({
        type: 'SECOND_ELEMENT_CLICKED_RECEIVED',
        selector: request.selector,
        timestamp: request.timestamp
      });
    } catch (e) {
      console.log('❌ 发送第二个元素点击消息到popup失败:', e);
    }

    sendResponse({ success: true });
  }
});