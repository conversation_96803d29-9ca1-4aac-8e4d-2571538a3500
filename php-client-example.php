<?php
/**
 * PHP客户端示例
 * 展示如何从其他PHP脚本触发Chrome扩展的点击序列
 */

class ChromeExtensionTrigger {
    private $serverUrl;
    
    public function __construct($serverUrl = 'http://127.0.0.1:8888') {
        $this->serverUrl = rtrim($serverUrl, '/');
    }
    
    /**
     * 触发点击序列
     */
    public function triggerClickSequence($data = []) {
        $url = $this->serverUrl . '/trigger';
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => 'Content-Type: application/json',
                'timeout' => 10
            ]
        ]);
        
        try {
            $response = file_get_contents($url, false, $context);
            if ($response === false) {
                throw new Exception('请求失败');
            }
            
            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('响应解析失败: ' . json_last_error_msg());
            }
            
            return $result;
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 检查服务器状态
     */
    public function getStatus() {
        $url = $this->serverUrl . '/status';
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => 'Content-Type: application/json',
                'timeout' => 5
            ]
        ]);
        
        try {
            $response = file_get_contents($url, false, $context);
            if ($response === false) {
                throw new Exception('请求失败');
            }
            
            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('响应解析失败: ' . json_last_error_msg());
            }
            
            return $result;
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 等待执行完成
     */
    public function waitForCompletion($timeout = 30) {
        $startTime = time();
        
        while (time() - $startTime < $timeout) {
            $status = $this->getStatus();
            
            if (!$status['success']) {
                return [
                    'success' => false,
                    'error' => '无法获取状态: ' . $status['error']
                ];
            }
            
            // 如果trigger为false且有确认时间，说明执行完成
            if (!$status['trigger'] && !empty($status['last_confirm_time'])) {
                return [
                    'success' => true,
                    'message' => '执行完成',
                    'status' => $status
                ];
            }
            
            sleep(1); // 等待1秒后再检查
        }
        
        return [
            'success' => false,
            'error' => '等待超时'
        ];
    }
    
    /**
     * 触发并等待完成
     */
    public function triggerAndWait($timeout = 30) {
        // 先触发
        $triggerResult = $this->triggerClickSequence();
        if (!$triggerResult['success']) {
            return $triggerResult;
        }
        
        echo "✅ 触发成功，等待执行完成...\n";
        
        // 等待完成
        $waitResult = $this->waitForCompletion($timeout);
        return $waitResult;
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    // 命令行模式
    echo "🚀 Chrome扩展触发器测试\n";
    echo "========================\n\n";
    
    $trigger = new ChromeExtensionTrigger();
    
    // 检查服务器状态
    echo "📊 检查服务器状态...\n";
    $status = $trigger->getStatus();
    if ($status['success']) {
        echo "✅ 服务器在线\n";
        echo "📈 触发次数: " . ($status['trigger_count'] ?? 0) . "\n";
        echo "⏰ 最后触发: " . ($status['last_trigger_time'] ?? '无') . "\n";
        echo "✅ 最后确认: " . ($status['last_confirm_time'] ?? '无') . "\n\n";
    } else {
        echo "❌ 服务器离线: " . $status['error'] . "\n";
        exit(1);
    }
    
    // 触发点击序列
    echo "🎯 触发点击序列...\n";
    $result = $trigger->triggerAndWait(30);
    
    if ($result['success']) {
        echo "🎉 执行成功！\n";
        echo "📊 最终状态:\n";
        print_r($result['status']);
    } else {
        echo "❌ 执行失败: " . $result['error'] . "\n";
    }
    
} else {
    // Web模式
    header('Content-Type: text/html; charset=utf-8');
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Chrome扩展触发器</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #005a87; }
            .result { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0; white-space: pre-wrap; }
        </style>
    </head>
    <body>
        <h1>🎯 Chrome扩展触发器</h1>
        
        <?php
        if (isset($_GET['action'])) {
            $trigger = new ChromeExtensionTrigger();
            
            echo '<div class="result">';
            
            switch ($_GET['action']) {
                case 'trigger':
                    echo "🚀 触发点击序列...\n";
                    $result = $trigger->triggerClickSequence();
                    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    break;
                    
                case 'status':
                    echo "📊 检查状态...\n";
                    $result = $trigger->getStatus();
                    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    break;
                    
                case 'trigger_and_wait':
                    echo "🎯 触发并等待完成...\n";
                    $result = $trigger->triggerAndWait(30);
                    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                    break;
            }
            
            echo '</div>';
        }
        ?>
        
        <p>
            <a href="?action=trigger"><button>触发点击序列</button></a>
            <a href="?action=status"><button>检查状态</button></a>
            <a href="?action=trigger_and_wait"><button>触发并等待</button></a>
        </p>
        
        <h2>📝 使用说明</h2>
        <ol>
            <li>确保PHP触发服务器已启动: <code>php -S 127.0.0.1:8888 php-trigger-server.php</code></li>
            <li>确保Chrome扩展已安装并启用</li>
            <li>点击上面的按钮测试功能</li>
        </ol>
        
        <h2>💻 命令行使用</h2>
        <p>也可以在命令行中运行: <code>php php-client-example.php</code></p>
    </body>
    </html>
    <?php
}
?>
