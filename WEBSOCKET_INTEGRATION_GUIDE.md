# WebSocket集成指南

## 🚀 概述

使用WebSocket实现Chrome扩展和PHP项目之间的实时通信，相比HTTP轮询具有以下优势：
- **低延迟**: 实时双向通信，无需轮询等待
- **高效率**: 减少网络请求，降低服务器负载
- **实时性**: 立即响应，支持实时确认

## 🏗️ 架构说明

```
PHP项目 ←→ WebSocket服务器 ←→ Chrome扩展
   ↓              ↓              ↓
发送触发请求   →  转发消息     →  执行点击操作
接收执行确认   ←  转发确认     ←  发送完成确认
```

### 组件说明
1. **WebSocket服务器**: 中央消息路由器，处理所有连接和消息转发
2. **Chrome扩展**: 连接到WebSocket服务器，接收触发指令
3. **PHP客户端**: 连接到WebSocket服务器，发送触发请求

## 🛠️ 安装和配置

### 1. 安装依赖
```bash
# 安装Composer依赖
composer install

# 或者手动安装
composer require ratchet/pawl textalk/websocket-client
```

### 2. 启动WebSocket服务器
```bash
php websocket-server.php
```

服务器启动后会显示：
```
✅ WebSocket服务器已启动
🔗 监听地址: ws://127.0.0.1:9998
📡 等待Chrome扩展和PHP项目连接...
```

### 3. 重新加载Chrome扩展
确保扩展已安装并重新加载以启用WebSocket功能。

## 💻 PHP项目集成

### 基本使用
```php
<?php
require_once 'ChromeExtensionTrigger.php';

$trigger = new ChromeExtensionTrigger();

// 触发Chrome扩展执行点击操作
$result = $trigger->triggerClick([
    'user_id' => 123,
    'action' => 'process_user'
]);

if ($result['success']) {
    echo "触发成功！消息ID: " . $result['message_id'];
} else {
    echo "触发失败: " . $result['error'];
}
?>
```

### 触发并等待完成
```php
<?php
$trigger = new ChromeExtensionTrigger();

// 触发并等待执行完成（最多等待30秒）
$result = $trigger->triggerAndWait([
    'order_id' => 'ORDER-2024-001',
    'priority' => 'high'
], 30);

if ($result['success']) {
    echo "执行完成！";
    echo "扩展ID: " . $result['extension_id'];
    echo "完成时间: " . $result['timestamp'];
} else {
    echo "执行失败: " . $result['error'];
}
?>
```

### 测试连接
```php
<?php
$trigger = new ChromeExtensionTrigger();

if ($trigger->testConnection()) {
    echo "WebSocket连接正常";
} else {
    echo "WebSocket连接失败，请检查服务器状态";
}
?>
```

## 🎯 实际应用场景

### 1. 用户注册后触发
```php
function onUserRegistered($userId, $userData) {
    $trigger = new ChromeExtensionTrigger('ws://127.0.0.1:9998', 'user_system');
    
    $result = $trigger->triggerClick([
        'event' => 'user_registered',
        'user_id' => $userId,
        'user_data' => $userData,
        'timestamp' => time()
    ]);
    
    if ($result['success']) {
        error_log("Chrome extension triggered for user registration: $userId");
        return true;
    } else {
        error_log("Failed to trigger Chrome extension: " . $result['error']);
        return false;
    }
}
```

### 2. 订单处理流程
```php
function processOrder($orderId) {
    $trigger = new ChromeExtensionTrigger('ws://127.0.0.1:9998', 'order_system');
    
    // 触发并等待完成，确保操作执行
    $result = $trigger->triggerAndWait([
        'event' => 'order_processing',
        'order_id' => $orderId,
        'priority' => 'high'
    ], 60); // 等待60秒
    
    if ($result['success']) {
        echo "订单处理完成，Chrome扩展操作已执行\n";
        return true;
    } else {
        echo "Chrome扩展操作失败: " . $result['error'] . "\n";
        return false;
    }
}
```

### 3. 定时任务集成
```php
// cron任务示例
function dailyMaintenanceTask() {
    $trigger = new ChromeExtensionTrigger('ws://127.0.0.1:9998', 'cron_system');
    
    // 测试连接
    if (!$trigger->testConnection()) {
        error_log("WebSocket服务器不可用，跳过Chrome扩展操作");
        return false;
    }
    
    $result = $trigger->triggerClick([
        'event' => 'daily_maintenance',
        'scheduled_time' => date('Y-m-d H:i:s'),
        'task_type' => 'automated'
    ]);
    
    if ($result['success']) {
        echo "定时任务已触发Chrome扩展\n";
        return true;
    } else {
        error_log("定时任务触发失败: " . $result['error']);
        return false;
    }
}
```

### 4. Web管理界面
```php
// 在管理后台中
if ($_POST['action'] === 'trigger_chrome_extension') {
    $trigger = new ChromeExtensionTrigger('ws://127.0.0.1:9998', 'admin_panel');
    
    $result = $trigger->triggerAndWait([
        'source' => 'admin_panel',
        'admin_user' => $_SESSION['admin_id'],
        'action' => $_POST['chrome_action'] ?? 'manual_trigger'
    ], 30);
    
    if ($result['success']) {
        $message = "Chrome扩展操作执行成功！";
        $messageType = 'success';
    } else {
        $message = "操作失败: " . $result['error'];
        $messageType = 'error';
    }
}
```

## 🔧 配置选项

### 自定义WebSocket地址
```php
// 如果WebSocket服务器部署在其他地址
$trigger = new ChromeExtensionTrigger('ws://your-server.com:9998', 'my_project');
```

### 自定义超时时间
```php
// 连接超时10秒，等待确认60秒
$trigger = new ChromeExtensionTrigger('ws://127.0.0.1:9998', 'my_project', 10);
$result = $trigger->triggerAndWait($data, 60);
```

### 项目名称标识
```php
// 使用项目名称便于在服务器日志中识别
$trigger = new ChromeExtensionTrigger('ws://127.0.0.1:9998', 'ecommerce_system');
```

## 🔍 监控和调试

### WebSocket服务器日志
服务器会输出详细的连接和消息日志：
```
[2024-01-01 12:00:00] 📡 Chrome扩展已连接 - 客户端:1 - extension_id_123
[2024-01-01 12:00:05] 📡 PHP项目已连接 - 客户端:2 - my_project
[2024-01-01 12:00:10] 📡 触发点击请求 - 客户端:2 - 发送到1个扩展
[2024-01-01 12:00:11] 📡 收到执行确认 - 客户端:1 - trigger_abc123
```

### Chrome扩展日志
打开Chrome开发者工具Console查看：
```
✅ WebSocket连接已建立
📡 收到WebSocket消息: {type: "trigger_click", data: {...}}
🎯 收到PHP项目的WebSocket触发请求
✅ 已通过WebSocket发送执行确认
```

### PHP客户端调试
```php
$trigger = new ChromeExtensionTrigger();

// 测试连接
if ($trigger->testConnection()) {
    echo "✅ WebSocket连接正常\n";
} else {
    echo "❌ WebSocket连接失败\n";
    echo "💡 请检查:\n";
    echo "   1. WebSocket服务器是否启动\n";
    echo "   2. 防火墙设置\n";
    echo "   3. 网络连接\n";
}
```

## ⚠️ 注意事项

### 安全考虑
1. **访问控制**: 在生产环境中添加身份验证
2. **SSL/TLS**: 使用WSS协议保护数据传输
3. **输入验证**: 验证所有WebSocket消息内容

### 性能优化
1. **连接复用**: 避免频繁建立和断开连接
2. **消息大小**: 控制消息内容大小
3. **超时设置**: 设置合理的连接和等待超时

### 部署建议
1. **进程管理**: 使用Supervisor等工具管理WebSocket服务器
2. **负载均衡**: 多实例部署时考虑负载均衡
3. **监控**: 添加健康检查和性能监控

## 🔄 故障排除

### 常见问题

#### 1. WebSocket连接失败
```bash
# 检查服务器是否启动
ps aux | grep websocket-server

# 检查端口是否监听
netstat -an | grep 9998

# 重启服务器
php websocket-server.php
```

#### 2. Chrome扩展无法连接
- 检查扩展是否已重新加载
- 确认manifest.json权限配置
- 查看扩展Console错误信息

#### 3. 消息发送失败
- 检查WebSocket服务器日志
- 验证消息格式是否正确
- 确认网络连接稳定

### 调试步骤
1. 启动WebSocket服务器并查看日志
2. 重新加载Chrome扩展
3. 运行PHP测试脚本
4. 检查所有组件的日志输出

## 🎉 优势总结

相比HTTP轮询方案，WebSocket方案具有：
- **延迟更低**: 实时通信，无轮询延迟
- **效率更高**: 减少网络请求，降低服务器负载
- **可靠性强**: 支持连接状态检测和自动重连
- **扩展性好**: 支持多客户端同时连接
- **实时确认**: 立即获得执行结果反馈

现在您可以享受高效的实时通信体验！
