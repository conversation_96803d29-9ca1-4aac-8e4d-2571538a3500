<?php
/**
 * PHP触发服务器
 * 用于与Chrome扩展通信，触发点击序列执行
 * 
 * 使用方法:
 * 1. 启动服务器: php -S 127.0.0.1:8888 php-trigger-server.php
 * 2. 触发点击: http://127.0.0.1:8888/trigger
 * 3. 检查状态: http://127.0.0.1:8888/status
 */

// 设置CORS头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, Accept');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 获取请求路径
$requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$method = $_SERVER['REQUEST_METHOD'];

// 日志函数
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message\n";
    error_log("[$timestamp] $message");
}

// 触发状态文件
$triggerFile = __DIR__ . '/trigger_status.json';

// 初始化触发状态
function initTriggerStatus() {
    global $triggerFile;
    if (!file_exists($triggerFile)) {
        $status = [
            'trigger' => false,
            'last_trigger_time' => null,
            'last_confirm_time' => null,
            'trigger_count' => 0
        ];
        file_put_contents($triggerFile, json_encode($status, JSON_PRETTY_PRINT));
    }
}

// 读取触发状态
function getTriggerStatus() {
    global $triggerFile;
    initTriggerStatus();
    $content = file_get_contents($triggerFile);
    return json_decode($content, true);
}

// 更新触发状态
function updateTriggerStatus($data) {
    global $triggerFile;
    $current = getTriggerStatus();
    $updated = array_merge($current, $data);
    file_put_contents($triggerFile, json_encode($updated, JSON_PRETTY_PRINT));
    return $updated;
}

// 路由处理
switch ($requestUri) {
    case '/':
        // 主页 - 显示API文档
        header('Content-Type: text/html; charset=utf-8');
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>PHP触发服务器</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
                .method { color: #007cba; font-weight: bold; }
                .url { color: #d63384; }
                button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
                button:hover { background: #005a87; }
                .status { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <h1>🚀 PHP触发服务器</h1>
            <p>用于与Chrome扩展通信，触发点击序列执行</p>
            
            <h2>📡 API端点</h2>
            <div class="endpoint">
                <span class="method">GET</span> <span class="url">/check-trigger</span><br>
                Chrome扩展用于检查是否有触发请求
            </div>
            <div class="endpoint">
                <span class="method">POST</span> <span class="url">/confirm-trigger</span><br>
                Chrome扩展用于确认执行完成
            </div>
            <div class="endpoint">
                <span class="method">GET</span> <span class="url">/trigger</span><br>
                触发点击序列执行
            </div>
            <div class="endpoint">
                <span class="method">GET</span> <span class="url">/status</span><br>
                查看当前状态
            </div>
            
            <h2>🎮 快速操作</h2>
            <button onclick="triggerClick()">触发点击序列</button>
            <button onclick="checkStatus()">检查状态</button>
            <button onclick="clearStatus()">清空状态</button>
            
            <div id="result" class="status"></div>
            
            <script>
                async function triggerClick() {
                    try {
                        const response = await fetch('/trigger');
                        const data = await response.json();
                        document.getElementById('result').innerHTML = '<strong>触发结果:</strong><br>' + JSON.stringify(data, null, 2);
                    } catch (error) {
                        document.getElementById('result').innerHTML = '<strong>错误:</strong> ' + error.message;
                    }
                }
                
                async function checkStatus() {
                    try {
                        const response = await fetch('/status');
                        const data = await response.json();
                        document.getElementById('result').innerHTML = '<strong>当前状态:</strong><br>' + JSON.stringify(data, null, 2);
                    } catch (error) {
                        document.getElementById('result').innerHTML = '<strong>错误:</strong> ' + error.message;
                    }
                }
                
                async function clearStatus() {
                    try {
                        const response = await fetch('/clear');
                        const data = await response.json();
                        document.getElementById('result').innerHTML = '<strong>清空结果:</strong><br>' + JSON.stringify(data, null, 2);
                    } catch (error) {
                        document.getElementById('result').innerHTML = '<strong>错误:</strong> ' + error.message;
                    }
                }
            </script>
        </body>
        </html>
        <?php
        break;
        
    case '/check-trigger':
        // Chrome扩展检查是否有触发请求
        if ($method === 'GET') {
            $status = getTriggerStatus();
            logMessage("扩展检查触发状态: " . ($status['trigger'] ? 'true' : 'false'));
            echo json_encode($status);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;
        
    case '/confirm-trigger':
        // Chrome扩展确认执行完成
        if ($method === 'POST') {
            $input = json_decode(file_get_contents('php://input'), true);
            $status = updateTriggerStatus([
                'trigger' => false,
                'last_confirm_time' => date('Y-m-d H:i:s'),
                'last_confirm_data' => $input
            ]);
            logMessage("扩展确认执行完成");
            echo json_encode(['success' => true, 'status' => $status]);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;
        
    case '/trigger':
        // 触发点击序列
        if ($method === 'GET') {
            $status = updateTriggerStatus([
                'trigger' => true,
                'last_trigger_time' => date('Y-m-d H:i:s'),
                'trigger_count' => getTriggerStatus()['trigger_count'] + 1,
                'trigger_data' => [
                    'source' => 'manual',
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
                ]
            ]);
            logMessage("手动触发点击序列 #" . $status['trigger_count']);
            echo json_encode(['success' => true, 'message' => '点击序列已触发', 'status' => $status]);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;
        
    case '/status':
        // 查看状态
        if ($method === 'GET') {
            $status = getTriggerStatus();
            echo json_encode($status);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;
        
    case '/clear':
        // 清空状态
        if ($method === 'GET') {
            $status = updateTriggerStatus([
                'trigger' => false,
                'last_trigger_time' => null,
                'last_confirm_time' => null,
                'trigger_count' => 0,
                'trigger_data' => null,
                'last_confirm_data' => null
            ]);
            logMessage("状态已清空");
            echo json_encode(['success' => true, 'message' => '状态已清空', 'status' => $status]);
        } else {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
        }
        break;
        
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Not found', 'path' => $requestUri]);
        break;
}
?>
