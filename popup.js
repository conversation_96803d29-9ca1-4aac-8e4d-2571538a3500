// 存储检测记录、下载记录、缓存跳过记录、新消息点击记录和元素点击记录
let detections = [];
let downloads = [];
let cacheSkips = [];
let newMessageClicks = [];
let elementClicks = [];

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
  loadDetections();
  updateStatus();
  
  // 监听来自background的消息
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.type === 'TARGET_DETECTED') {
      addDetection(request);
    } else if (request.type === 'DOWNLOAD_SUCCESS') {
      addDownloadRecord(request);
    } else if (request.type === 'CACHE_SKIP') {
      addCacheSkipRecord(request);
    } else if (request.type === 'NEW_MESSAGE_CLICKED') {
      addNewMessageClickRecord(request);
    } else if (request.type === 'FIRST_ELEMENT_CLICKED_RECEIVED') {
      addElementClickRecord(request, '第一个元素');
    } else if (request.type === 'SECOND_ELEMENT_CLICKED_RECEIVED') {
      addElementClickRecord(request, '第二个元素');
    }
  });
  

  
  // 清空按钮事件
  document.getElementById('clearBtn').addEventListener('click', function() {
    detections = [];
    downloads = [];
    cacheSkips = [];
    newMessageClicks = [];
    elementClicks = [];
    saveDetections();
    saveDownloads();
    saveCacheSkips();
    saveNewMessageClicks();
    saveElementClicks();
    updateDetectionsDisplay();
    updateDownloadsDisplay();
    updateCacheSkipsDisplay();
    updateNewMessageClicksDisplay();
    updateElementClicksDisplay();
  });
  

});

// 添加检测记录
function addDetection(data) {
  const detection = {
    url: data.url,
    urlType: data.urlType,
    timestamp: data.timestamp,
    time: new Date(data.timestamp).toLocaleString('zh-CN')
  };
  
  detections.unshift(detection); // 添加到开头
  
  // 限制记录数量
  if (detections.length > 50) {
    detections = detections.slice(0, 50);
  }
  
  saveDetections();
  updateDetectionsDisplay();
  
  // 在控制台打印
  console.log(`🔍 检测到微信公众号${data.urlType}:`, data.url);
  console.log('⏰ 时间:', detection.time);
}

// 保存检测记录到存储
function saveDetections() {
  chrome.storage.local.set({ 'detections': detections });
}

// 保存下载记录到存储
function saveDownloads() {
  chrome.storage.local.set({ 'downloads': downloads });
}

// 保存缓存跳过记录到存储
function saveCacheSkips() {
  chrome.storage.local.set({ 'cacheSkips': cacheSkips });
}

// 保存新消息点击记录到存储
function saveNewMessageClicks() {
  chrome.storage.local.set({ 'newMessageClicks': newMessageClicks });
}

// 保存元素点击记录到存储
function saveElementClicks() {
  chrome.storage.local.set({ 'elementClicks': elementClicks });
}

// 加载检测记录
function loadDetections() {
  chrome.storage.local.get(['detections', 'downloads', 'cacheSkips', 'newMessageClicks', 'elementClicks'], function(result) {
    detections = result.detections || [];
    downloads = result.downloads || [];
    cacheSkips = result.cacheSkips || [];
    newMessageClicks = result.newMessageClicks || [];
    elementClicks = result.elementClicks || [];
    updateDetectionsDisplay();
    updateDownloadsDisplay();
    updateCacheSkipsDisplay();
    updateNewMessageClicksDisplay();
    updateElementClicksDisplay();
  });
}

// 更新检测记录显示
function updateDetectionsDisplay() {
  const container = document.getElementById('detections');
  const countElement = document.getElementById('detectionCount');
  
  countElement.textContent = detections.length;
  
  if (detections.length === 0) {
    container.innerHTML = '<div class="no-detections">暂无检测记录</div>';
    return;
  }
  
  container.innerHTML = detections.map(detection => `
    <div class="detection-item">
      <div class="detection-time">${detection.time}</div>
      <div class="detection-type">${detection.urlType}</div>
      <div class="detection-url">${detection.url}</div>
    </div>
  `).join('');
}

// 添加下载记录
function addDownloadRecord(data) {
  const download = {
    url: data.url,
    filename: data.filename,
    msgid: data.msgid,
    tofakeid: data.tofakeid,
    downloadId: data.downloadId,
    timestamp: data.timestamp,
    overwritten: data.overwritten,
    time: new Date(data.timestamp).toLocaleString('zh-CN')
  };
  
  downloads.unshift(download); // 添加到开头
  
  // 限制记录数量
  if (downloads.length > 20) {
    downloads = downloads.slice(0, 20);
  }
  
  saveDownloads();
  updateDownloadsDisplay();
  
  // 在控制台打印
  console.log('✅ 图片下载成功:', data.filename);
  console.log('🆔 msgid:', data.msgid || '未找到');
  console.log('👤 tofakeid:', data.tofakeid || '未找到');
  console.log('📁 文件路径:', data.url);
  console.log('⏰ 时间:', download.time);
  if (data.overwritten) {
    console.log('🔄 文件已覆盖');
  }
}

// 添加缓存跳过记录
function addCacheSkipRecord(data) {
  const cacheSkip = {
    msgid: data.msgid,
    tofakeid: data.tofakeid,
    remainingTime: data.remainingTime,
    timestamp: data.timestamp,
    time: new Date(data.timestamp).toLocaleString('zh-CN')
  };
  
  cacheSkips.unshift(cacheSkip); // 添加到开头
  
  // 限制记录数量
  if (cacheSkips.length > 10) {
    cacheSkips = cacheSkips.slice(0, 10);
  }
  
  saveCacheSkips();
  updateCacheSkipsDisplay();
  
  // 在控制台打印
  console.log('⏰ 缓存跳过下载:', data.msgid);
  console.log('👤 tofakeid:', data.tofakeid);
  console.log('⏱️ 剩余缓存时间:', data.remainingTime, '秒');
  console.log('⏰ 时间:', cacheSkip.time);
}

// 添加新消息点击记录
function addNewMessageClickRecord(data) {
  const clickRecord = {
    timestamp: data.timestamp,
    time: new Date(data.timestamp).toLocaleString('zh-CN')
  };
  
  newMessageClicks.unshift(clickRecord); // 添加到开头
  
  // 限制记录数量
  if (newMessageClicks.length > 10) {
    newMessageClicks = newMessageClicks.slice(0, 10);
  }
  
  saveNewMessageClicks();
  updateNewMessageClicksDisplay();
  
  // 在控制台打印
  console.log('🔔 新消息元素已点击');
  console.log('⏰ 时间:', clickRecord.time);
}

// 添加元素点击记录
function addElementClickRecord(data, elementType) {
  const clickRecord = {
    timestamp: data.timestamp,
    time: new Date(data.timestamp).toLocaleString('zh-CN'),
    selector: data.selector,
    elementType: elementType
  };

  elementClicks.unshift(clickRecord); // 添加到开头

  // 限制记录数量
  if (elementClicks.length > 20) {
    elementClicks = elementClicks.slice(0, 20);
  }

  saveElementClicks();
  updateElementClicksDisplay();

  // 在控制台打印
  console.log('🎯 元素已点击:', elementType);
  console.log('🎯 选择器:', data.selector);
  console.log('⏰ 时间:', clickRecord.time);
}



// 更新下载记录显示
function updateDownloadsDisplay() {
  const container = document.getElementById('downloads');
  if (!container) return;
  
  if (downloads.length === 0) {
    container.innerHTML = '<div class="no-downloads">暂无下载记录</div>';
    return;
  }
  
  container.innerHTML = downloads.map(download => `
    <div class="download-item">
      <div class="download-time">${download.time}</div>
      <div class="download-filename">${download.filename} ${download.overwritten ? '🔄' : ''}</div>
      <div class="download-msgid">msgid: ${download.msgid || '未找到'}</div>
      <div class="download-tofakeid">tofakeid: ${download.tofakeid || '未找到'}</div>
      <div class="download-url">${download.url}</div>
    </div>
  `).join('');
}

// 更新缓存跳过记录显示
function updateCacheSkipsDisplay() {
  const container = document.getElementById('cacheSkips');
  if (!container) return;
  
  if (cacheSkips.length === 0) {
    container.innerHTML = '<div class="no-cache-skips">暂无缓存跳过记录</div>';
    return;
  }
  
  container.innerHTML = cacheSkips.map(skip => `
    <div class="cache-skip-item">
      <div class="cache-skip-time">${skip.time}</div>
      <div class="cache-skip-msgid">msgid: ${skip.msgid}</div>
      <div class="cache-skip-tofakeid">tofakeid: ${skip.tofakeid}</div>
      <div class="cache-skip-remaining">剩余时间: ${skip.remainingTime}秒</div>
    </div>
  `).join('');
}

// 更新新消息点击记录显示
function updateNewMessageClicksDisplay() {
  const container = document.getElementById('newMessageClicks');
  if (!container) return;
  
  if (newMessageClicks.length === 0) {
    container.innerHTML = '<div class="no-new-message-clicks">暂无新消息点击记录</div>';
    return;
  }
  
  container.innerHTML = newMessageClicks.map(click => `
    <div class="new-message-click-item">
      <div class="new-message-click-time">${click.time}</div>
      <div class="new-message-click-status">🔔 新消息已点击</div>
    </div>
  `).join('');
}

// 更新元素点击记录显示
function updateElementClicksDisplay() {
  const container = document.getElementById('elementClicks');
  if (!container) return;

  if (elementClicks.length === 0) {
    container.innerHTML = '<div class="no-element-clicks">暂无元素点击记录</div>';
    return;
  }

  container.innerHTML = elementClicks.map(click => `
    <div class="element-click-item">
      <div class="element-click-time">${click.time}</div>
      <div class="element-click-type">🎯 ${click.elementType}</div>
      <div class="element-click-selector">${click.selector}</div>
    </div>
  `).join('');
}



// 更新状态显示
function updateStatus() {
  chrome.runtime.sendMessage({ type: 'GET_STATUS' }, function(response) {
    if (response) {
      document.getElementById('status').textContent = '监听中';
      const urls = response.targetUrls.map(url => url.replace('https://', '')).join(', ');
      document.getElementById('targetUrl').textContent = urls;
    }
  });
} 