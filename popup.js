// 存储检测记录、下载记录、缓存跳过记录、新消息点击记录和getnewmsgnum响应记录
let detections = [];
let downloads = [];
let cacheSkips = [];
let newMessageClicks = [];
let getNewMsgNumResponses = [];

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
  loadDetections();
  updateStatus();
  
  // 监听来自background的消息
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.type === 'TARGET_DETECTED') {
      addDetection(request);
    } else if (request.type === 'DOWNLOAD_SUCCESS') {
      addDownloadRecord(request);
    } else if (request.type === 'CACHE_SKIP') {
      addCacheSkipRecord(request);
    } else if (request.type === 'NEW_MESSAGE_CLICKED') {
      addNewMessageClickRecord(request);
    } else if (request.type === 'GETNEWMSGNUM_RESPONSE_RECEIVED') {
      // 添加getnewmsgnum响应记录
      addGetNewMsgNumRecord(request);
    }
  });
  
  // 加载getnewmsgnum响应记录
  loadGetNewMsgNumResponses();
  
  // 清空按钮事件
  document.getElementById('clearBtn').addEventListener('click', function() {
    detections = [];
    downloads = [];
    cacheSkips = [];
    newMessageClicks = [];
    getNewMsgNumResponses = [];
    saveDetections();
    saveDownloads();
    saveCacheSkips();
    saveNewMessageClicks();
    saveGetNewMsgNumResponses();
    updateDetectionsDisplay();
    updateDownloadsDisplay();
    updateCacheSkipsDisplay();
    updateNewMessageClicksDisplay();
    updateGetNewMsgNumResponsesDisplay();
  });
  
  // 测试按钮事件
  document.getElementById('testBtn').addEventListener('click', function() {
    console.log('🧪 点击测试按钮');
    
    // 向所有标签页发送测试消息
    chrome.tabs.query({}, function(tabs) {
      console.log(`🧪 发送测试消息到 ${tabs.length} 个标签页`);
      
      let sentCount = 0;
      tabs.forEach(tab => {
        try {
          chrome.tabs.sendMessage(tab.id, {
            type: 'TEST_GETNEWMSGNUM',
            timestamp: Date.now()
          }, response => {
            if (chrome.runtime.lastError) {
              console.log(`🧪 标签页 ${tab.id} 发送失败:`, chrome.runtime.lastError.message);
            } else {
              sentCount++;
              console.log(`🧪 标签页 ${tab.id} 发送成功`);
            }
          });
        } catch (error) {
          console.log(`🧪 发送到标签页 ${tab.id} 出错:`, error);
        }
      });
      
      // 直接在popup中显示测试信息
      const testInfo = document.createElement('div');
      testInfo.className = 'test-info';
      testInfo.innerHTML = `<p>🧪 监听器已激活</p>
                           <p>getnewmsgnum是POST请求，请在微信公众平台页面操作，触发原始请求</p>
                           <p>请查看控制台输出</p>`;
      document.body.appendChild(testInfo);
      
      // 5秒后移除测试信息
      setTimeout(() => {
        if (testInfo.parentNode) {
          testInfo.parentNode.removeChild(testInfo);
        }
      }, 5000);
    });
  });
});

// 添加检测记录
function addDetection(data) {
  const detection = {
    url: data.url,
    urlType: data.urlType,
    timestamp: data.timestamp,
    time: new Date(data.timestamp).toLocaleString('zh-CN')
  };
  
  detections.unshift(detection); // 添加到开头
  
  // 限制记录数量
  if (detections.length > 50) {
    detections = detections.slice(0, 50);
  }
  
  saveDetections();
  updateDetectionsDisplay();
  
  // 在控制台打印
  console.log(`🔍 检测到微信公众号${data.urlType}:`, data.url);
  console.log('⏰ 时间:', detection.time);
}

// 保存检测记录到存储
function saveDetections() {
  chrome.storage.local.set({ 'detections': detections });
}

// 保存下载记录到存储
function saveDownloads() {
  chrome.storage.local.set({ 'downloads': downloads });
}

// 保存缓存跳过记录到存储
function saveCacheSkips() {
  chrome.storage.local.set({ 'cacheSkips': cacheSkips });
}

// 保存新消息点击记录到存储
function saveNewMessageClicks() {
  chrome.storage.local.set({ 'newMessageClicks': newMessageClicks });
}

// 加载检测记录
function loadDetections() {
  chrome.storage.local.get(['detections', 'downloads', 'cacheSkips', 'newMessageClicks'], function(result) {
    detections = result.detections || [];
    downloads = result.downloads || [];
    cacheSkips = result.cacheSkips || [];
    newMessageClicks = result.newMessageClicks || [];
    updateDetectionsDisplay();
    updateDownloadsDisplay();
    updateCacheSkipsDisplay();
    updateNewMessageClicksDisplay();
  });
}

// 更新检测记录显示
function updateDetectionsDisplay() {
  const container = document.getElementById('detections');
  const countElement = document.getElementById('detectionCount');
  
  countElement.textContent = detections.length;
  
  if (detections.length === 0) {
    container.innerHTML = '<div class="no-detections">暂无检测记录</div>';
    return;
  }
  
  container.innerHTML = detections.map(detection => `
    <div class="detection-item">
      <div class="detection-time">${detection.time}</div>
      <div class="detection-type">${detection.urlType}</div>
      <div class="detection-url">${detection.url}</div>
    </div>
  `).join('');
}

// 添加下载记录
function addDownloadRecord(data) {
  const download = {
    url: data.url,
    filename: data.filename,
    msgid: data.msgid,
    tofakeid: data.tofakeid,
    downloadId: data.downloadId,
    timestamp: data.timestamp,
    overwritten: data.overwritten,
    time: new Date(data.timestamp).toLocaleString('zh-CN')
  };
  
  downloads.unshift(download); // 添加到开头
  
  // 限制记录数量
  if (downloads.length > 20) {
    downloads = downloads.slice(0, 20);
  }
  
  saveDownloads();
  updateDownloadsDisplay();
  
  // 在控制台打印
  console.log('✅ 图片下载成功:', data.filename);
  console.log('🆔 msgid:', data.msgid || '未找到');
  console.log('👤 tofakeid:', data.tofakeid || '未找到');
  console.log('📁 文件路径:', data.url);
  console.log('⏰ 时间:', download.time);
  if (data.overwritten) {
    console.log('🔄 文件已覆盖');
  }
}

// 添加缓存跳过记录
function addCacheSkipRecord(data) {
  const cacheSkip = {
    msgid: data.msgid,
    tofakeid: data.tofakeid,
    remainingTime: data.remainingTime,
    timestamp: data.timestamp,
    time: new Date(data.timestamp).toLocaleString('zh-CN')
  };
  
  cacheSkips.unshift(cacheSkip); // 添加到开头
  
  // 限制记录数量
  if (cacheSkips.length > 10) {
    cacheSkips = cacheSkips.slice(0, 10);
  }
  
  saveCacheSkips();
  updateCacheSkipsDisplay();
  
  // 在控制台打印
  console.log('⏰ 缓存跳过下载:', data.msgid);
  console.log('👤 tofakeid:', data.tofakeid);
  console.log('⏱️ 剩余缓存时间:', data.remainingTime, '秒');
  console.log('⏰ 时间:', cacheSkip.time);
}

// 添加新消息点击记录
function addNewMessageClickRecord(data) {
  const clickRecord = {
    timestamp: data.timestamp,
    time: new Date(data.timestamp).toLocaleString('zh-CN')
  };
  
  newMessageClicks.unshift(clickRecord); // 添加到开头
  
  // 限制记录数量
  if (newMessageClicks.length > 10) {
    newMessageClicks = newMessageClicks.slice(0, 10);
  }
  
  saveNewMessageClicks();
  updateNewMessageClicksDisplay();
  
  // 在控制台打印
  console.log('🔔 新消息元素已点击');
  console.log('⏰ 时间:', clickRecord.time);
}

// 添加getnewmsgnum响应记录
function addGetNewMsgNumRecord(data) {
  const record = {
    url: data.url,
    method: data.method,
    status: data.status,
    newTotalMsgCount: data.newTotalMsgCount,
    timestamp: data.timestamp,
    time: new Date(data.timestamp).toLocaleString('zh-CN')
  };
  
  getNewMsgNumResponses.unshift(record); // 添加到开头
  
  // 限制记录数量
  if (getNewMsgNumResponses.length > 10) {
    getNewMsgNumResponses = getNewMsgNumResponses.slice(0, 10);
  }
  
  saveGetNewMsgNumResponses();
  updateGetNewMsgNumResponsesDisplay();
  
  // 在控制台打印
  console.log('📊 收到getnewmsgnum响应记录:');
  console.log('📊 URL:', data.url);
  console.log('📊 方法:', data.method);
  console.log('📊 newTotalMsgCount:', data.newTotalMsgCount);
  console.log('📊 时间:', record.time);
}

// 保存getnewmsgnum响应记录到存储
function saveGetNewMsgNumResponses() {
  chrome.storage.local.set({ 'getNewMsgNumResponses': getNewMsgNumResponses });
}

// 加载getnewmsgnum响应记录
function loadGetNewMsgNumResponses() {
  chrome.storage.local.get(['getNewMsgNumResponses', 'getnewmsgnumResponses'], function(result) {
    // 尝试两种可能的键名
    getNewMsgNumResponses = result.getNewMsgNumResponses || result.getnewmsgnumResponses || [];
    updateGetNewMsgNumResponsesDisplay();
  });
}

// 更新下载记录显示
function updateDownloadsDisplay() {
  const container = document.getElementById('downloads');
  if (!container) return;
  
  if (downloads.length === 0) {
    container.innerHTML = '<div class="no-downloads">暂无下载记录</div>';
    return;
  }
  
  container.innerHTML = downloads.map(download => `
    <div class="download-item">
      <div class="download-time">${download.time}</div>
      <div class="download-filename">${download.filename} ${download.overwritten ? '🔄' : ''}</div>
      <div class="download-msgid">msgid: ${download.msgid || '未找到'}</div>
      <div class="download-tofakeid">tofakeid: ${download.tofakeid || '未找到'}</div>
      <div class="download-url">${download.url}</div>
    </div>
  `).join('');
}

// 更新缓存跳过记录显示
function updateCacheSkipsDisplay() {
  const container = document.getElementById('cacheSkips');
  if (!container) return;
  
  if (cacheSkips.length === 0) {
    container.innerHTML = '<div class="no-cache-skips">暂无缓存跳过记录</div>';
    return;
  }
  
  container.innerHTML = cacheSkips.map(skip => `
    <div class="cache-skip-item">
      <div class="cache-skip-time">${skip.time}</div>
      <div class="cache-skip-msgid">msgid: ${skip.msgid}</div>
      <div class="cache-skip-tofakeid">tofakeid: ${skip.tofakeid}</div>
      <div class="cache-skip-remaining">剩余时间: ${skip.remainingTime}秒</div>
    </div>
  `).join('');
}

// 更新新消息点击记录显示
function updateNewMessageClicksDisplay() {
  const container = document.getElementById('newMessageClicks');
  if (!container) return;
  
  if (newMessageClicks.length === 0) {
    container.innerHTML = '<div class="no-new-message-clicks">暂无新消息点击记录</div>';
    return;
  }
  
  container.innerHTML = newMessageClicks.map(click => `
    <div class="new-message-click-item">
      <div class="new-message-click-time">${click.time}</div>
      <div class="new-message-click-status">🔔 新消息已点击</div>
    </div>
  `).join('');
}

// 更新getnewmsgnum响应记录显示
function updateGetNewMsgNumResponsesDisplay() {
  const container = document.getElementById('getNewMsgNumResponses');
  if (!container) {
    // 如果容器不存在，创建一个
    const section = document.createElement('div');
    section.className = 'getnewmsgnum-list';
    section.innerHTML = `
      <h3>getnewmsgnum响应记录</h3>
      <div id="getNewMsgNumResponses"></div>
    `;
    
    // 插入到清空按钮之前
    const clearBtn = document.getElementById('clearBtn');
    if (clearBtn && clearBtn.parentNode) {
      clearBtn.parentNode.insertBefore(section, clearBtn);
    } else {
      document.body.appendChild(section);
    }
    
    // 重新获取容器
    updateGetNewMsgNumResponsesDisplay();
    return;
  }
  
  if (getNewMsgNumResponses.length === 0) {
    container.innerHTML = '<div class="no-getnewmsgnum">暂无getnewmsgnum响应记录</div>';
    return;
  }
  
  container.innerHTML = getNewMsgNumResponses.map(record => `
    <div class="getnewmsgnum-item">
      <div class="getnewmsgnum-time">${record.time}</div>
      <div class="getnewmsgnum-method">${record.method} ${record.status}</div>
      <div class="getnewmsgnum-count">newTotalMsgCount: ${record.newTotalMsgCount}</div>
      <div class="getnewmsgnum-url">${record.url}</div>
    </div>
  `).join('');
}

// 更新状态显示
function updateStatus() {
  chrome.runtime.sendMessage({ type: 'GET_STATUS' }, function(response) {
    if (response) {
      document.getElementById('status').textContent = '监听中';
      const urls = response.targetUrls.map(url => url.replace('https://', '')).join(', ');
      document.getElementById('targetUrl').textContent = urls;
    }
  });
} 